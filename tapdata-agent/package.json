{"name": "boot-loader", "version": "1.0.0", "description": "", "main": "tapdata.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"log4js": "^6.3.0", "md5": "^2.2.1", "prompts": "^2.4.2", "socket.io-client": "^2.3.0", "systeminformation": "^4.19.1", "tar": "^5.0.5", "uuid": "^3.3.3", "yamljs": "^0.3.0"}, "dependencies": {"ali-oss": "^6.23.0", "argsplit": "latest", "axios": "^1.11.0", "compressing": "^1.6.2", "crypto-js": "^3.1.9-1", "express": "^5.1.0", "express-ws": "^4.0.0", "getprocesses": "^1.1.1", "hashcode": "^1.0.3", "make-dir": "^3.0.0", "moment": "^2.29.3", "mongodb": "^3.4.1", "pkg": "^5.3.0", "ws": "latest"}, "pkg": {"scripts": "./tapdata.js,./os-monitor/*.js", "assets": "./node_modules/**/**/**/**/**/**/**/**/**/**/**"}, "overrides": {"yamljs": {"glob": "^11.0.3"}}}