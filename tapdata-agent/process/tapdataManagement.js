const basic = require('../basic');
//const getProcesses = require("getprocesses");
const fs = require('fs');
//const msg = require('../sendMessage');
const path = require('path');
const {sign} = require("../util/AKSK");
const WebSocket = require("ws");
const {MongoClient} = require("mongodb");
const moment = require("moment");
const child_process = require("child_process");
const axios = require('axios');
let deployInit;
let urls;
let user;
let pdkFiles = [];
let pdkFilesName = [];
let ws;
let reportInfo = {'server':'management','level':'INFO'};
let deployTryTimes = 0;
let deployTryTime = 0;
const connectionSendMsg = require("../connection").sandMsg;
let deployed = false;
module.exports = {
    doStartFrontend:async function (conf, arguments) {//启动前台程序
        if (basic.isDebug(arguments)) {
            console.info("frontend is starting...");
        }
        let needTar = false;
        if (basic.isDebug(arguments)) {
            console.info("check frontend gz file");
        }
        let arr = fs.readdirSync(conf.COMPONENTS_DIR);
        for (let x in arr) {
            if (/frontend-+[\s\S]*?\.tar\.gz$/.test(arr[x])) {
                needTar = true;
                try {
                    fs.accessSync(conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME, fs.constants.F_OK);
                    fs.rmdirSync(conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME, {'recursive': true});
                } catch (e) {
                }
                const tar = require('tar');
                console.info("Unpack the frontend files...");
                try {
                    await tar.x(
                        {
                            file: conf.COMPONENTS_DIR + "/" + arr[x],
                            C: conf.COMPONENTS_DIR + '/'
                        }
                    );
                }catch (e) {
                    console.info("Unpack the frontend files error:" + e);
                    return;
                }//.then(_ => {
                    let arr1 = fs.readdirSync(conf.COMPONENTS_DIR);
                    for (let y in arr1) {
                        if (/frontend-+[\s\S]*?$/.test(arr1[y])) {
                            if(!(/frontend-+[\s\S]*?\.tar\.gz$/.test(arr[x]))) {
                                try {
                                    let from = conf.COMPONENTS_DIR + "/" + arr1[y];
                                    let to = conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME;
                                    if (conf['_windows']) {
                                        from = from.replace(/\//g, '\\');
                                        to = to.replace(/\//g, '\\');
                                    }
                                    fs.accessSync(from, fs.constants.F_OK);
                                    fs.renameSync(from, to);
                                    if (basic.isDebug(arguments)) {
                                        console.info("rename frontend dir");
                                    }
                                } catch (e) {
                                    console.info(e)
                                }
                            }
                            try {
                                fs.accessSync(conf.COMPONENTS_DIR + "/" + arr[x], fs.constants.F_OK);
                                fs.unlinkSync(conf.COMPONENTS_DIR + "/" + arr[x], {'recursive': true});
                                if (basic.isDebug(arguments)) {
                                    console.info("remove frontend gz file");
                                }
                            } catch (e) {
                                console.info(e)
                            }
                        }
                    }
                    //if(!isGod){
                        //msg(JSON.stringify(arguments));
                    //}
                    //startFun(conf, arguments);
                //});
            }
        }
        //if (!needTar) {
            //if(!isGod){
                //msg(JSON.stringify(arguments));
            //}
            //startFun(conf, arguments);
        //}
    },
    stopFrontend:async function (conf, isRestart,connect) {//停止前台程序
            let list ;//= await require("getprocesses").getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
        if (conf._windows) {
            list = await require("../ps_win").getProcessesWindows();
        }else {
            list = await require("getprocesses").getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
        }
            let frontend = conf.COMPONENTS_DIR +"/"+ conf.FRONTEND_DIR_NAME +"/server/index.js";
        let pdk = 'tapdata-pdk-deploy.jar.XXX';
            if(conf._windows){
                frontend = frontend.replace(/\//g,'\\');
            }
            if(basic.isTMJava(conf)){
                frontend = path.join(conf.COMPONENTS_DIR,'tm.jar');
                pdk = path.join(conf.TAPDATA_HOME,'lib','pdk-deploy.jar');
            }
            let isKilled = false;
            console.info('pdk file:',pdk);
            for (let i = 0; i < list.length; i++) {
                if (list[i].command.indexOf(frontend) >= 0 || list[i].arguments.join(' ').indexOf(frontend) >= 0) {
                    basic.killPid(list[i].pid,conf);
                    isKilled = true;
                }
                if (list[i].command.indexOf(pdk) >= 0 || list[i].arguments.join(' ').indexOf(pdk) >= 0) {
                    console.info('kill pdk')
                    basic.killPid(list[i].pid,conf);
                }
            }
            if(isKilled && basic.isEmpty((await basic.getStatus(conf)).frontMPid)) {
                connect.write('Frontend is down.',reportInfo);
                //console.info('Frontend is down.');
                let msgObj = {
                    level:"warn",
                    system:"agent",
                    msg:"manageSeverStoppedSuccessfully",
                    title:"manageSeverStoppedSuccessfully",
                    serverName:"",
                    sourceId:conf.uuid
                };
                //connectionSendMsg('postMessage',msgObj);
            }else if(isKilled){
                await stopFrontend(conf, isRestart,connect);
            }else if(!isKilled){
                connect.write('Frontend is already stopped. Skipping stop operation.',reportInfo);
                //console.info('Frontend is already stopped. Skipping stop operation.');
            }
            if(isRestart)
                await this.startFrontend(conf,arguments,connect);
    },
    startFrontend:async function (conf, arguments,connect) {
        if(!basic.isTMJava(conf)) {
            try {
                let datasource = path.join(conf.TAPDATA_HOME, 'components', 'tapdata-management', 'server', 'datasources.json');
                let data = fs.readFileSync(datasource, 'utf-8');
                if (conf.ssl === 'true' || conf.ssl === true) {
                    data = data.replace('"ssl": false,', '"ssl": true,');
                } else {
                    data = data.replace('"ssl": true,', '"ssl": false,');
                }
                fs.writeFileSync(datasource, data, 'utf-8');
            } catch (e) {
                connect.write('write datasource error.', reportInfo);
                console.log('write datasource error.');
            }
        }
        let env = process.env;
        env.TAPDATA_MONGO_URI = conf.uri;//'mongodb://127.0.0.1:27017/tapdata';
        env.TAPDATA_PORT = conf.tapdata_port;
        env.API_SERVER_PORT = conf.api_server_port;//'3080';
        env.TAPDATA_MONGO_CONN = conf.mongo_conn;//'mongodb://127.0.0.1:27017/tapdata';
        env.TAPDATA_WORK_DIR = conf.WORK_DIR;//'/root/.tapdata';
        env.TAPDATA_HOME = conf.TAPDATA_HOME;
        env.NODE_HOME = conf.NODE_HOME;
        //env.PATH = conf.NODE_HOME + ':' + env.PATH;
        env.port = env.TAPDATA_PORT;
        env.WORK_DIR = conf.WORK_DIR;
        env.APP_HOME = conf.TAPDATA_HOME;
        env.APP_HOME.CONF_SH_FILE = conf.WORK_DIR + "/tapdata.conf";
        env.ssl = (conf.ssl==='true'||conf.ssl===true)?true:false;
        env.sslKey = conf.MONGO_SSL_CERT_KEY;
        env.sslCert = conf.MONGO_SSL_CERT_KEY;
        env.sslPass = conf.mongo_sslPEMKeyFilePassword;
        env.sslCA = conf.MONGO_SSL_CA;
        env.sslCAPath = conf.sslCAPath;
        env.sslCertKeyPath = conf.sslCertKeyPath;
        try{
            if (fs.existsSync(path.join(conf.TAPDATA_HOME, '.config'))) {
                let c = fs.readFileSync(path.join(conf.TAPDATA_HOME,'.config'),'utf8').toString();
                c = JSON.parse(c);
                if(c.oem && c.oem !== '' && c.oem !== 'production'){
                    env.oem = c.oem;
                }
            }
        }catch (e) {}
        conf.TAPDATA_TM_JAVA_OPTS = conf.TAPDATA_TM_JAVA_OPTS.trim() ;//+ " -Dspring.config.location=file:"+conf.WORK_DIR+"/application.yml -Dlogging.config=file:"+conf.TAPDATA_HOME+"/etc/log4j2.yml ";
        let newArgs = []
        if(conf.TAPDATA_TM_JAVA_OPTS && conf.TAPDATA_TM_JAVA_OPTS !== "")
            newArgs = conf.TAPDATA_TM_JAVA_OPTS.split(" ");
        /*
        newArgs.push('-XX:+HeapDumpOnOutOfMemoryError');
        let HeapDumpPath = "-XX:HeapDumpPath=" + path.join(conf.WORK_DIR,'oom-heap-') + moment().format('YYYYMMDDHH') + '.dmp';
        newArgs.push(HeapDumpPath);
        newArgs.push('-XX:-OmitStackTraceInFastThrow');
        */
        if(conf.frontend_worker_count && conf.frontend_worker_count !== "") {
            env.frontend_worker_count = conf.frontend_worker_count;
        }
        let version = require("../version")(false,conf);
        //console.info('version',version);
        env.tapdataVersion = version;
        let command = 'node';
        let execString1 = conf.COMPONENTS_DIR + "/tapdata-management/server/index.js";
        let cwd1 = conf.COMPONENTS_DIR + "/tapdata-management/";
        if (conf._windows) {
            execString1 = execString1.replace(/\//g, '\\');
            cwd1 = cwd1.replace(/\//g, '\\');
        }
        execString1 = [execString1];
        basic.deleteTMStatus(conf);
        if(basic.isTMJava(conf)){
            command = 'java';
            cwd1 = conf.COMPONENTS_DIR;
            newArgs.push('-XX:+HeapDumpOnOutOfMemoryError');
            let HeapDumpPath = "-XX:HeapDumpPath=" + path.join(conf.WORK_DIR,'tm-oom-heap-') + moment().format('YYYYMMDDHH') + '.dmp';
            newArgs.push(HeapDumpPath);
            newArgs.push('-XX:-OmitStackTraceInFastThrow');
            execString1 = [
                '-jar',
                '-Dserver.port='+conf.tapdata_port,
                '-server',
                ...newArgs
                //'-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=3050',
                //'-XX:CompressedClassSpaceSize=512m',
                //'-XX:MetaspaceSize=512m',
                //'-XX:MaxMetaspaceSize=1024m',
                //'-Dspring.data.mongodb.uri=${mongodb_uri}',
                //path.join(conf.COMPONENTS_DIR,'tm.jar'),
                //'--spring.config.additional-location=file:' + path.join(conf.TAPDATA_HOME,'etc','application-tm.yml'),
                //'--logging.config=file:' + path.join(conf.TAPDATA_HOME,'etc','logback.xml')
            ]
            execString1.push('-Dspring.data.mongodb.default.uri=${mongodb_uri}');
            execString1.push('-Dspring.data.mongodb.log.uri=${mongodb_uri}');
            execString1.push('-Dspring.data.mongodb.obs.uri=${mongodb_uri}');
            let javaPath = "java";
            try{
                fs.accessSync(path.join(conf.TAPDATA_HOME, 'lib','java','bin','java.exe'), fs.constants.F_OK);
                javaPath = path.join(conf.TAPDATA_HOME, 'lib','java','bin','java.exe');
            }catch (e) {
            }
            try{
                fs.accessSync(path.join(conf.TAPDATA_HOME, 'lib','java','bin','java'), fs.constants.F_OK);
                javaPath = path.join(conf.TAPDATA_HOME, 'lib','java','bin','java');
            }catch (e) {
            }
            console.info("JAVA PATH:",javaPath);
            let javaVersion = await getJavaVersion(javaPath);
            if(javaVersion && javaVersion !== ''){
                if ((javaVersion === "17.")) {
                    execString1.push('--add-opens=java.base/java.lang=ALL-UNNAMED');
                    execString1.push('--add-opens=java.base/java.util=ALL-UNNAMED');
                    execString1.push('--add-opens=java.base/java.security=ALL-UNNAMED');
                    execString1.push('--add-opens=java.base/sun.security.rsa=ALL-UNNAMED');
                    execString1.push('--add-opens=java.base/sun.security.x509=ALL-UNNAMED');
                    execString1.push('--add-opens=java.base/sun.security.util=ALL-UNNAMED');
                    execString1.push('--add-opens=java.xml/com.sun.org.apache.xerces.internal.jaxp.datatype=ALL-UNNAMED');
                }
            }
            execString1.push(path.join(conf.COMPONENTS_DIR,'tm.jar'));
            execString1.push('--spring.config.additional-location=file:' + path.join(conf.TAPDATA_HOME,'etc','application-tm.yml'));
            execString1.push('--logging.config=file:' + path.join(conf.TAPDATA_HOME,'etc','logback.xml'));
        }
        let stdio = ['ignore', 'ignore', 'pipe'];//outLog
        if (basic.isDebug(arguments)) {
            console.info("start frontend env :");
            console.info(JSON.stringify(env));
            connect.write('start frontend env :' + JSON.stringify(env),reportInfo);
            console.info("start frontend args :");
            console.info(execString1);
            connect.write('start frontend args :' + execString1,reportInfo);
            console.info("start frontend cwd :");
            console.info(cwd1);
            connect.write('start frontend cwd :' + cwd1,reportInfo);
        }
        let subProcess1 = require('child_process').spawn(command, execString1, {
            'env': env,
            'cwd': cwd1,
            'stdio': stdio,
            'windowsHide': true,
            'detached': true
        });
        const log4js = require("log4js");
        log4js.configure({
            appenders: { cheese: {compress: true, alwaysIncludePattern:true , daysToKeep: 7, type: "dateFile", filename: conf.WORK_DIR + "/logs/tapdata-agent/frontendDebug.log" } },
            categories: { default: { appenders: ["cheese"], level: "error" } }
        });
        const logger = log4js.getLogger("cheese");
        subProcess1.stderr.on('data',(msg)=>{
            logger.error(msg.toString());
        });
        subProcess1.on('error', (err) => {
            console.error('Failed to start subprocess.',err);
        });
        subProcess1.unref();

        let res = false;
        let errorMsg = '';
        const now = new Date().getTime();
        console.info('begin check');
        let waitingMsg = "<<<< Waiting for the TM to start ";
        connect.write("Waiting for the TM to start",reportInfo)
        let waitingTimes = 1;
        while(true){
            const TMStatus = basic.readTMStatus(conf)
            if(TMStatus){
                if(TMStatus.status === 'ok') {
                    res = true;
                    break
                }else{
                    res = false;
                    errorMsg = TMStatus.msg || '';
                    break
                }
            }
            if(new Date().getTime() - now > 300000){
                res = false;
                errorMsg = 'start TM timeout';
                console.info('TM time out res=false')
                break
            }
            await sleep(1000);
            waitingTimes++;
            let point = '';
            /*for(let z=0;z<waitingTimes % 3 ;z++){
                point = point + '.';
            }*/
            if(waitingTimes % 3 === 0 ){
                point = '-';
            }else if(waitingTimes % 3 === 1 ){
                point = '/';
            }else{
                point = "\\";
            }
            connect.write(waitingMsg+point,reportInfo);
        }

        if(!res) {
            console.info('check faile');
            connect.write("Frontend start fail.", reportInfo);
            connect.write("Frontend start error: "+errorMsg+".", reportInfo);
            let msgObj = {
                level: "info",
                system: "agent",
                msg: "manageSeverStartedFail",
                title: "manageSeverStartedFail",
                serverName: "",
                sourceId: conf.uuid
            };
            setTimeout(() => {
                connectionSendMsg('postMessage', msgObj);
            }, 2000);
            throw('Frontend start fail.');
        }
        //if(!basic.isEmpty((await basic.getStatus(conf)).frontMPid)) {
            connect.write('Frontend started.', reportInfo);
            //console.log('frontend server started.');
        let msgObj = {
            level:"info",
            system:"agent",
            msg:"manageSeverStartedSuccessfully",
            title:"manageSeverStartedSuccessfully",
            serverName:"",
            sourceId:conf.uuid
        };
        try {
            fs.accessSync(path.join(conf.TAPDATA_HOME, 'lib', 'pdk-deploy.jar'), fs.constants.F_OK);
            //connect.write('begin deploy init', reportInfo);
            //console.info("begin deploy init");
            // deployInit = false;
            // deployed = false;
            // deployTryTimes = 0;
            // deployTryTime = new Date().getTime();
            //await deploy(conf, connect, arguments);
            const deployConnector = require('./deployConnector')
            deployConnector.deployConnector(conf, arguments, connect)
        } catch (e) {
            console.info(e);
        }
        setTimeout(()=> {
            if (typeof connectionSendMsg === 'function') {
                connectionSendMsg('postMessage',msgObj);
            }
        },2000);
    },
    deploy
};


async function deploy(conf,connect,arguments){
//is pkd
    deployInit = false;
    deployed = false;
    deployTryTimes = 0;
    deployTryTime = new Date().getTime();

    let backend_url = conf.backend_url;
    urls = backend_url.split(',');
    for (let i = 0; i < urls.length; i++) {
        if (urls[i].indexOf('https://') > -1){
            urls[i] = 'wss://' + urls[i].split('/api')[0].replace('https://', '');
        }
        else {
            urls[i] = 'ws://' + urls[i].split('/api')[0].replace('http://', '');
        }
        /*
        if(urls[i].indexOf('cloud.tapdata.net') > -1){
            urls[i] = urls[i].replace('/console', '');
        }
         */
    }
    const connectorDir = path.join(conf.TAPDATA_HOME,'connectors','dist')
    pdkFiles = fs.readdirSync(connectorDir);
    for(let x in pdkFiles){
        if(pdkFiles[x].includes('tdd')){
            continue;
        }
        pdkFilesName.push(pdkFiles[x]);
        pdkFiles[x] = path.join(connectorDir,pdkFiles[x]);
    }
    //console.info('deploys:',pdkFiles);
    connect.write(`Found ${pdkFilesName.length} connectors in directory ${connectorDir}`);
    if (pdkFilesName.length > 0) {
        await conn(conf,connect,arguments);
    }
}

function conn(conf,connect,arguments) {
    return new Promise(function (resolve, reject) {
        //console.info('conn deployInit:', deployInit);
        deployTryTimes++;
        if (deployInit === false) {
            if(deployTryTimes === 1) {
                connect.write('Try to connect to TM for deploy connector (use [./tapdata status] to show deploy detail)...',reportInfo);
                //console.info('Try to connect to TM for deploy connector...');
            } else {
                let useTime = (new Date().getTime() - deployTryTime)/1000;
                connect.write('<<<< Try to connect to TM for deploy connector (use [./tapdata status] to show deploy detail)... waiting ' + useTime + 'sec.',reportInfo);
                console.info(`Deploy connector connection attempt ${deployTryTimes}, waiting ${useTime}sec`);

                // 如果等待时间超过10分钟，输出详细的调试信息
                if (useTime > 600) {
                    console.error('Deploy connector connection timeout exceeded 10 minutes');
                    console.error('Backend URLs:', urls);
                    console.error('Current attempt:', deployTryTimes);
                    connect.write('Deploy connector connection timeout - please check TM service status and network connectivity', reportInfo);
                }
            }
            //console.info('urls',urls);
            const RandomNum = GetRandomNum(urls.length - 1);
            //console.info('RandomNum',RandomNum);
            let url = urls[RandomNum];
            //console.info('backend_url',conf.backend_url);
            let bUrl = conf.backend_url.split(',')[RandomNum].replace('/api/','').replace('/api','');
            //console.info('bUrl:', bUrl);
            if (conf.accessKey && conf.accessKey !== '') {
                url = url.replace('/tm_xdfwdsax', '/tm');
                const u = url + '/ws/cluster/?access_token=PM51PWywjurdBe8KwXNpUkbB4yceUhOGwyREly6AmxF0Pr2b1qOynCdX06CTpk3G';
                url = sign(conf.accessKey, conf.secretKey, u);
            } else {
                url = url + '/ws/cluster/?access_token=PM51PWywjurdBe8KwXNpUkbB4yceUhOGwyREly6AmxF0Pr2b1qOynCdX06CTpk3G';
            }
            //console.info('url:', url);
            if (url.indexOf('wss://') > -1) {
                ws = new WebSocket(url, [], {handshakeTimeout: 2000, rejectUnauthorized: false});
            } else {
                ws = new WebSocket(url, [], {handshakeTimeout: 2000});//'ws://localhost:3000',[],{handshakeTimeout:500}
            }
            ws.on('open',async function open() {
                deployInit = true;
                console.info('Successfully connected to TM for deploy connector');
                connect.write('Connected to TM, starting connector deployment...', reportInfo);
                try {
                    await basic.deploy(conf,connect,reportInfo,bUrl,pdkFilesName,true);
                    await callInitApiPromise(conf, connect);
                    deployed = true;
                    pdkFilesName = [];
                    console.info('Connector deployment completed successfully');
                }catch (e) {
                    console.error('Error during connector deployment:', e);
                    connect.write(`Connector deployment error: ${e.message || e}`, reportInfo);
                }finally {
                    ws.close();
                    resolve();
                }
            });
            ws.on('message', msg => {
            });
            ws.on('error', (error) => {
                console.error('WebSocket connection error:', error.message || error);
                console.error('Failed to connect to TM at:', url);
                connect.write(`WebSocket connection error: ${error.message || error}`, reportInfo);
            });
            ws.on('close', () => {
                if(!deployed) {
                    //console.info('is Closed');
                    setTimeout(async function () {
                        await conn(conf, connect, arguments);
                        resolve();
                    }, 2000);
                }else{
                    deployed = false;
                }
            });
        }else{
            resolve();
        }
    })
}

function GetRandomNum(Range) {
    let Rand = Math.random();
    return (Math.round(Rand * Range));
}

function sleep (time) {
    return new Promise((resolve) => setTimeout(resolve, time));
}

function getJavaVersion(javaPath){
    return new Promise((resolve,reject)=>{
        let str = javaPath + " -version";
        child_process.exec(str,{windowsHide:true}, (error, stdout, stderr) => {
                if (error) {
                    reject();
                }
                let JDKVersion = stderr.split("version")[1].split('"')[1].substring(0, 3);
                resolve(JDKVersion);
            }
        );
    })
}



/**
 * Promise 版本的初始化 API 调用
 * @param {Object} conf - 配置对象
 * @param {Object} connect - 连接对象，用于输出日志
 * @returns {Promise<boolean>} - 成功返回 true，失败返回 false
 */
async function callInitApiPromise(conf, connect) {
    return new Promise((resolve) => {
        try {
            // 获取后端 URL
            let backend_url = conf.backend_url;
            if (!backend_url) {
                connect.write('Backend URL not configured, skipping initialization.',
                    {'server': 'management', 'level': 'WARN'});
                resolve(false);
                return;
            }

            // 处理多个 URL 的情况，取第一个
            let urls = backend_url.split(',');
            let baseUrl = urls[0].replace('/api', '').replace(/\/$/, '');
            let initUrl = `${baseUrl}/api/init`;

            // 配置 axios 选项
            const axiosOptions = {
                url: initUrl,
                method: 'GET',
                timeout: 30000, // 30秒超时
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'TapData-Agent'
                },
                httpsAgent: new (require('https').Agent)({
                    rejectUnauthorized: false // 忽略 HTTPS 证书验证
                })
            };

            // 发送 GET 请求到 /api/init
            axios(axiosOptions)
                .then(response => {
                    if (response.status === 200) {
                        resolve(true);
                    } else {
                        console.warn(`HTTP ${response.status}: ${response.data || 'No response body'}`);
                        resolve(false);
                    }
                })
                .catch(error => {
                    console.warn(`Request error: ${error.message}`);
                    resolve(false);
                });

        } catch (error) {
            console.warn(`Exception in callInitApiPromise: ${error.message}`);
            resolve(false);
        }
    });
}