application:
  title: 'TM'
  version: '@project.version@-@profile.active@'
  commit_version: '@env.GIT_COMMIT_VERSION@'
  description: 'Tapdata Manager'
  build: '@maven.build.timestamp@'
  admin-account: '<EMAIL>'

spring:
  servlet:
    multipart:
      max-file-size: ${SERVER_UPLOAD_FILE_MAX_SIZE:300MB}
      max-request-size: ${SERVER_UPLOAD_REQUEST_MAX_SIZE:300MB}
      enabled: true
  application:
    name: TCMApplication

  thymeleaf:
    cache: false

  data:
    mongodb:
      cursorBatchSize: 1000
      default:
        uri: ${TAPDATA_MONGO_URI:mongodb://127.0.0.1:27017/tapdata_v29}
      ssl: ${ssl:false}
      caPath: ${sslCAPath:''}
      keyPath: ${sslCertKeyPath:''}

  messages:
    encoding: UTF-8

  mvc:
    static-path-pattern: /**
    path match:
      matching-strategy: ant_path_matcher
    format:
      date-time: yyyyMM-dd HH:mm:ss
    async:
      request-timeout: 1000
  jackson:
    default-property-inclusion: non_null

  codec:
    max-in-memory-size: 10MB
    thymeleaf:
      encoding=UTF-8
      content-type=text/html
      cache=false
      mode=LEGACYHTML5
    messages:
      encoding: UTF-8
      cache-seconds: 1
      basename: messages
  web:
    resources:
      static-locations: file:${TAPDATA_HOME:.}/components/webroot/
      chain:
        strategy:
          content:
            enabled: true
            paths: /static, /*.js, /*.js.gz
        enabled: true
        compressed: false
        cache: true
      cache:
        period: 7d
        cachecontrol:
          max-age: 24h
          cache-public: true

# https://springdoc.org/springdoc-properties.html
springdoc:
  enabled: true
  version: '@project.version@'
  api-docs:
    path: /api-docs
    groups:
      enabled: true
  swagger-ui:
    path: /swagger-ui
    display-request-duration: true
    groups-order: DESC
    operationsSorter: method
    show-actuator: true
  group-configs:
    - group: All
      packages-to-scan:
        - com.tapdata.tm
    - group: observability
      packages-to-scan:
        - com.tapdata.tm.observability.controller

server:
  port: ${TAPDATA_TM_PORT:3000}
  error:
    path: /error
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024
  http2:
    enabled: true
  tomcat:
    uri-encoding: UTF-8
    max-threads: 1000 #最大工作线程数量
    min-spare-threads: 100 #最小工作线程数量
    max-connections: 1000 #一瞬间最大支持的并发的连接数
    accept-count: 20 #等待队列长度
  max-http-request-header-size: 2MB

mail:
  host: smtp.exmail.qq.com
  user: <EMAIL>
  password: ENC(aU03+YWxQ+WbjOyzLyG0oKxdLUkSz3cf)

jasypt:
  encryptor:
    password: nmyswls

management:
  endpoints:
    web:
      exposure:
        include: "health, info"
  metrics:
    tags:
      application: 'TMApplication'

access:
  token:
    ttl: 1209600

mongodb_uri: ${TAPDATA_MONGO_URI}

task:
  reset:
    times: 2  #任务重置重试次数
    interval: 30000 #任务重置重试间隔，单位毫秒
    timeoutInterval: 50 #任务重置引擎没有返回时的超时时间，单位秒

report:
  url:
    measurementId: 'G-XWF69Y4KY3'
    apiSecret: 'noI4OfyaSuCOaFbr7AKbxA'
    clientId: '796104786.**********'
  data:
    oss: ${REPORT_DATA_OSS:false}

cache:
  expire: 10 #缓存过期时间，单位分钟
  initialCapacity: 100 #初始化缓存容量
  maximumSize: 500 #最大缓存数

db-lock:
    owner: "${TAPDATA_TM_ID:}"
    expire-seconds: 180
    heartbeat-seconds: 60

# OpenAPI Generator Configuration
openapi:
  generator:
    jar:
      path: auto  # Auto-detect between development and production paths
    template:
      path: auto  # Auto-detect between development and production paths
    temp:
      dir: ${java.io.tmpdir}
    java:
      version: 11
  setting:
    where-max-deep: ${openapi.setting.whereMaxDeep:10}
