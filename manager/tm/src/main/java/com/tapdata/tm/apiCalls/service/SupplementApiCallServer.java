package com.tapdata.tm.apiCalls.service;

import com.tapdata.tm.apiCalls.dto.ApiCallDto;
import com.tapdata.tm.apiCalls.vo.WorkerCallsInfo;
import com.tapdata.tm.apiServer.entity.WorkerCallEntity;
import com.tapdata.tm.apiServer.utils.PercentileCalculator;
import com.tapdata.tm.config.security.UserDetail;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> href="<EMAIL>">Gavin'Xiao</a>
 * <AUTHOR> href="https://github.com/11000100111010101100111">Gavin'Xiao</a>
 * @version v1.0 2025/9/26 16:20 Create
 * @description
 */
@Service
@Setter(onMethod_ = {@Autowired})
@Slf4j
public class SupplementApiCallServer {
    ApiCallService apiCallService;
    MongoTemplate mongoOperations;
    WorkerCallServiceImpl workerCallService;

    public void supplement(List<ApiCallDto> saveApiCallParamList, UserDetail loginUser) {
        if (CollectionUtils.isEmpty(saveApiCallParamList)) {
            return;
        }
        apiCallService.save(saveApiCallParamList, loginUser);
        try (WorkerCallsInfoGenerator generator = new WorkerCallsInfoGenerator(this::callUpdate)) {
            Map<String, Map<String, List<WorkerCallsInfo>>> collect = saveApiCallParamList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(
                            ApiCallDto::getWorkOid,
                            Collectors.groupingBy(
                                    ApiCallDto::getAllPathId,
                                    Collectors.mapping(this::convertToWorkerCallsInfo,
                                            Collectors.collectingAndThen(
                                                    Collectors.toList(),
                                                    this::sortAndProcessList
                                            )
                                    )
                            )
                    ));
            collect.forEach((workerId, apiIdMap) ->
                    apiIdMap.forEach((apiId, infos) ->
                            generator.append(infos)
                    )
            );
        }
    }


    private WorkerCallsInfo convertToWorkerCallsInfo(ApiCallDto dto) {
        WorkerCallsInfo callsInfo = new WorkerCallsInfo();
        callsInfo.setWorkOid(dto.getWorkOid());
        callsInfo.setApiGatewayUuid(dto.getApi_gateway_uuid());
        callsInfo.setApiId(dto.getAllPathId());
        callsInfo.setLatency(dto.getLatency());
        callsInfo.setCode(dto.getCode());
        callsInfo.setReqTime(dto.getReqTime());
        callsInfo.setResTime(dto.getResTime());
        return callsInfo;
    }

    private List<WorkerCallsInfo> sortAndProcessList(List<WorkerCallsInfo> list) {
        return list.stream()
                .sorted(Comparator.comparing(WorkerCallsInfo::getReqTime))
                .toList();
    }

    protected void callUpdate(List<WorkerCallEntity> infos) {
        if (CollectionUtils.isEmpty(infos)) {
            return;
        }
        List<Criteria> or = new ArrayList<>();
        for (WorkerCallEntity entity : infos) {
            Criteria criteria = Criteria.where(WorkerCallServiceImpl.Tag.TIME_START).is(entity.getTimeStart())
                    .and(WorkerCallServiceImpl.Tag.DELETE).is(entity.getDelete())
                    .and(WorkerCallServiceImpl.Tag.TIME_GRANULARITY).is(entity.getTimeGranularity())
                    .and(WorkerCallServiceImpl.Tag.PROCESS_ID).is(entity.getProcessId())
                    .and(WorkerCallServiceImpl.Tag.API_ID).is(entity.getApiId())
                    .and(WorkerCallServiceImpl.Tag.WORK_OID).is(entity.getWorkOid());
            or.add(criteria);
        }
        Query query = Query.query(new Criteria().orOperator(or));
        List<WorkerCallEntity> entities = mongoOperations.find(query, WorkerCallEntity.class);
        Map<String, Map<String, List<WorkerCallEntity>>> collect = entities.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(
                        WorkerCallEntity::getWorkOid,
                        Collectors.groupingBy(
                                WorkerCallEntity::getApiId
                        )
                ));
        infos.forEach(entity -> {
                    Optional.ofNullable(collect.get(entity.getWorkOid()))
                            .map(e -> e.get(entity.getApiId()))
                            .ifPresent(vos -> vos.forEach(item -> {
                                entity.setReqCount(Optional.ofNullable(entity.getErrorCount()).orElse(0L) + Optional.ofNullable(item.getErrorCount()).orElse(0L));
                                entity.setReqCount(Optional.ofNullable(entity.getReqCount()).orElse(0L) + Optional.ofNullable(item.getReqCount()).orElse(0L));
                                Optional.ofNullable(item.getDelays()).ifPresent(ds -> {
                                    List<Long> delays = Optional.ofNullable(entity.getDelays()).orElse(new ArrayList<>());
                                    delays.addAll(ds);
                                    entity.setDelays(delays);
                                });
                            }));
                    Long p50 = PercentileCalculator.calculatePercentile(entity.getDelays(), 0.5d);
                    Long p95 = PercentileCalculator.calculatePercentile(entity.getDelays(), 0.95d);
                    Long p99 = PercentileCalculator.calculatePercentile(entity.getDelays(), 0.99d);
                    Double errorRate = entity.getReqCount() <= 0 ? 0 : (0.1d * entity.getErrorCount() / entity.getReqCount());
                    entity.setReqCount(Optional.of(entity.getReqCount()).orElse(0L));
                    Double rps = entity.getReqCount() > 0L ? entity.getReqCount() * 1.0D / 60D : 0D;
                    entity.setP50(p50);
                    entity.setP95(p95);
                    entity.setP99(p99);
                    entity.setErrorRate(errorRate);
                    entity.setRps(rps);
                }
        );
        workerCallService.bulkUpsert(infos);
    }
}
