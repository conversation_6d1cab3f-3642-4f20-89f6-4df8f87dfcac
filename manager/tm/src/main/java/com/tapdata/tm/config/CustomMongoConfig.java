package com.tapdata.tm.config;

import com.mongodb.MongoException;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.InsertManyOptions;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.data.mongodb.core.CollectionOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Configuration
public class CustomMongoConfig {

    private static final Logger logger = LoggerFactory.getLogger(CustomMongoConfig.class);

    private final MongoTemplate mongoTemplate;

    public CustomMongoConfig(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @PostConstruct
    public void init() {
        ClassPathScanningCandidateComponentProvider provider =
                new ClassPathScanningCandidateComponentProvider(false);
        provider.addIncludeFilter(new AnnotationTypeFilter(CappedCollection.class));
        provider.findCandidateComponents("com.tapdata.tm")
                .forEach(beanDef -> {
                    try {
                        Class<?> clazz = Class.forName(beanDef.getBeanClassName());
                        Document entityAnnotation = clazz.getAnnotation(Document.class);
                        if (null == entityAnnotation) {
                            return;
                        }
                        CappedCollection annotation = clazz.getAnnotation(CappedCollection.class);
                        if (null == annotation) {
                            return;
                        }
                        String name = entityAnnotation.value();
                        if (annotation.capped()) {
                            handleCappedCollection(name, annotation);
                        }
                    } catch (Exception e) {
                        logger.warn("Failed to process capped collection for class: {}, error: {}",
                                beanDef.getBeanClassName(), e.getMessage());
                    }
                });
    }

    protected void handleCappedCollection(String collectionName, CappedCollection annotation) {
        try {
            if (!mongoTemplate.collectionExists(collectionName)) {
                createCappedCollection(collectionName, annotation);
            } else {
                updateExistingCollection(collectionName, annotation);
            }
        } catch (Exception e) {
            logger.error("Failed to handle capped collection: {}, error: {}", collectionName, e.getMessage(), e);
        }
    }

    protected void createCappedCollection(String collectionName, CappedCollection annotation) {
        logger.info("Creating new capped collection: {}", collectionName);
        CollectionOptions collectionOptions = CollectionOptions.empty()
                .capped();
        long length = annotation.maxLength();
        if (length <= 0L) {
            length = 100000L;
        }
        long size = annotation.maxMemory();
        if (size <= 0L) {
            size = 1L << 40;
        }
        CollectionOptions options = collectionOptions.size(size).maxDocuments(length);
        mongoTemplate.createCollection(collectionName, options);
        logger.info("Successfully created capped collection: {}", collectionName);
    }

    protected void updateExistingCollection(String collectionName, CappedCollection annotation) {
        try {
            CollectionStats stats = getCollectionStats(collectionName);
            if (!stats.isCapped()) {
                recreateCappedCollection(collectionName, annotation);
            } else {
                if (needsUpdate(stats, annotation)) {
                    recreateCappedCollection(collectionName, annotation);
                } else {
                    logger.debug("Capped collection {} attributes are up to date", collectionName);
                }
            }
        } catch (Exception e) {
            logger.error("Failed to update existing collection: {}, error: {}", collectionName, e.getMessage(), e);
        }
    }

    protected CollectionStats getCollectionStats(String collectionName) {
        try {
            MongoDatabase database = mongoTemplate.getDb();
            org.bson.Document collStats = database.runCommand(
                    new org.bson.Document("collStats", collectionName)
            );

            boolean capped = collStats.getBoolean("capped", false);
            long maxSize = Optional.ofNullable(collStats.get("maxSize"))
                    .map(String::valueOf)
                    .map(Long::parseLong)
                    .orElse(-1L);
            long max = Optional.ofNullable(collStats.get("max"))
                    .map(String::valueOf)
                    .map(Long::parseLong)
                    .orElse(0L);

            return new CollectionStats(capped, maxSize, max);
        } catch (Exception e) {
            logger.error("Failed to get collection stats for: {}, error: {}", collectionName, e.getMessage());
            return new CollectionStats(false, 0L, 0L);
        }
    }

    protected boolean needsUpdate(CollectionStats stats, CappedCollection annotation) {
        if (annotation.maxMemory() > 0L) {
            return stats.getMax() != annotation.maxLength() ||
                    stats.getMaxSize() != annotation.maxMemory();
        }
        return stats.getMax() != annotation.maxLength();
    }

    protected void recreateCappedCollection(String collectionName, CappedCollection annotation) {
        try {
            String backupCollectionName = collectionName + "_backup_" + System.currentTimeMillis();
            MongoDatabase database = mongoTemplate.getDb();
            database.getCollection(collectionName).renameCollection(
                    new com.mongodb.MongoNamespace(database.getName(), backupCollectionName)
            );
            createCappedCollection(collectionName, annotation);
            MongoCollection<org.bson.Document> newCollection = database.getCollection(collectionName);
            MongoCollection<org.bson.Document> backupCollection = database.getCollection(backupCollectionName);
            int batchSize = 2000;
            long beforeCount = backupCollection.countDocuments();
            List<org.bson.Document> buffer = new ArrayList<>(batchSize);
            try (MongoCursor<org.bson.Document> cursor = backupCollection.find().iterator()) {
                while (cursor.hasNext()) {
                    buffer.add(cursor.next());
                    if (buffer.size() == batchSize) {
                        try {
                            newCollection.insertMany(buffer, new InsertManyOptions().ordered(false));
                        } catch (MongoException e) {
                            logger.warn("Batch insert reached capped collection limits, stopping migration.");
                            break;
                        }
                        buffer = new ArrayList<>();
                    }
                }
            } finally {
                if (!buffer.isEmpty()) {
                    try {
                        newCollection.insertMany(buffer, new InsertManyOptions().ordered(false));
                    } catch (MongoException e) {
                        logger.warn("Final batch insert reached capped collection limits.");
                    }
                }
            }
            long afterCount = newCollection.countDocuments();
            if (afterCount >= beforeCount || backupCollection.countDocuments() <= 0L) {
                backupCollection.drop();
            }
        } catch (Exception e) {
            logger.error("Failed to recreate capped collection {}: {}", collectionName, e.getMessage(), e);
        }
    }


    @Getter
    public static class CollectionStats {
        private final boolean capped;
        private final long maxSize;
        private final long max;

        public CollectionStats(boolean capped, long maxSize, long max) {
            this.capped = capped;
            this.maxSize = maxSize;
            this.max = max;
        }
    }
}
