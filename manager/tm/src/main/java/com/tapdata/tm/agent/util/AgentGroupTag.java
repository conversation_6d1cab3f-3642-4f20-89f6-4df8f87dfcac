package com.tapdata.tm.agent.util;

public class AgentGroupTag {
    public static final String TAG_DELETE = "is_delete";
    public static final String TAG_NAME = "name";
    public static final String TAG_GROUP_ID = "groupId";
    public static final String TAG_AGENT_IDS = "agentIds";
    public static final String TAG_PROCESS_ID = "process_id";
    public static final String TAG_WORKER_TYPE = "worker_type";
    public static final String TAG_CONNECTOR = "connector";
    public static final String TAG_ACCESS_NODE_TYPE = "accessNodeType";
    public static final String TAG_ACCESS_NODE_PROCESS_ID_LIST = "accessNodeProcessIdList";

    public static final int MAX_AGENT_GROUP_NAME_LENGTH = 60;

    public static final String GROUP_NOT_FUND = "group.not.fund";
}
