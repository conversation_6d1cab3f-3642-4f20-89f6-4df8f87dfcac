package com.tapdata.tm.task.service.batchin.constant;

public class KeyWords {
    public static final String EMPTY = "";
    public static final String FROMAT = "%s.%s";
    public static final String VERSION = "version";
    public static final String DATABASE = "database";
    public static final String SCHEMA = "schema";
    public static final String DATABASES = "databases";
    public static final String SCHEMAS = "schemas";
    public static final String TABLE = "table";
    public static final String TABLES = "tables";
    public static final String PROJECT = "project";
    public static final String QUERIES = "queries";
    public static final String CONTENT = "content";
    public static final String PATH = "path";
    public static final String COLLECTIONS = "collections";
    public static final String CONNECTION_ID = "connectionId";
    public static final String MAPPINGS = "mappings";


    public static final String NAME = "name";
    public static final String ID = "id";
    public static final String TARGET_MODEL_NAME = "targetModelName";

    public static final String CHILDREN = "children";
    public static final String SETTINGS = "settings";
    public static final String COLUMNS = "columns";
    public static final String COLUMN = "column";
    public static final String TABLE_NAME = "tableName";
    public static final String UPDATE_WRITE = "updateWrite";
    public static final String TYPE = "type";
    public static final String NEW_DOCUMENT = "NEW_DOCUMENT";
    public static final String EMBEDDED_DOCUMENT = "EMBEDDED_DOCUMENT";
    public static final String EMBEDDED_DOCUMENT_ARRAY = "EMBEDDED_DOCUMENT_ARRAY";
    public static final String EMBEDDED_PATH = "embeddedPath";
    public static final String UPDATE_INTO_ARRAY = "updateIntoArray";
    public static final String FIELDS = "fields";
    public static final String FIELD = "field";
    public static final String SOURCE = "source";
    public static final String TARGET = "target";
    public static final String TARGET_PATH = "targetPath";
    public static final String IS_PRIMARY_KEY = "isPrimaryKey";
    public static final String ARRAY_KEYS = "arrayKeys";
    public static final String MERGE_TYPE = "mergeType";
    public static final String JOIN_KEYS = "joinKeys";
    public static final String FOREIGN_KEY = "foreignKey";

    public static final String INCLUDED = "included";
    public static final String PRIMITIVE = "primitive";
    public static final String COLLECTION_ID = "collectionId";
    public static final String RELATIONSHIPS = "relationships";
    public static final String CALCULATED_FIELDS = "calculatedFields";
    public static final String EXPRESSION = "expression";
    public static final String FULL = "full";
    public static final String IS_PK = "isPk";
    public static final String SYNC = "sync";
    public static final String NODE = "Node";
    public static final String IS_TRANSFORMED = "isTransformed";
    public static final String OPERATIONS = "operations";
    public static final String OP = "op";
    public static final String OPERAND = "operand";
}
