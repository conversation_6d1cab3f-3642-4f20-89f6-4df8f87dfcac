//package com.tapdata.tm.user.vo;
//
//import com.tapdata.tm.user.entity.GuideData;
//import com.tapdata.tm.user.entity.Notification;
//import lombok.Data;
//
//@Data
//public class UserInfoVo {
//    private String id;
//    private String accessCode;
//    private String username;
//    private boolean emailVerified;
//    private String externalUserId; // authing or eCloud user id
//    private String userId;
//    private String isPrimary;
//    private boolean isCompleteGuide;
//    private int accountStatus;
//    private Notification notification;
//
//    private GuideData guideData;
//}
