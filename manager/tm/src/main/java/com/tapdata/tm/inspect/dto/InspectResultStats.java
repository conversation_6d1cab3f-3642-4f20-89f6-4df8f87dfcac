//package com.tapdata.tm.inspect.dto;
//
//import com.tapdata.tm.inspect.bean.Source;
//import lombok.EqualsAndHashCode;
//import lombok.Getter;
//import lombok.Setter;
//import lombok.ToString;
//
//import java.io.Serializable;
//import java.util.Date;
//
///**
// * <AUTHOR> @ gmail.com>
// * @date 2020/9/20 3:03 下午
// * @description
// */
//@Setter
//@Getter
//@ToString
//@EqualsAndHashCode
//public class InspectResultStats implements Serializable {
//
//  private String taskId;
//  private Source source;
//  private Source target;
//  private long start;
//  private long end;
//  private String status;
//  private String errorMsg;
//  private String result;
//  private double progress;
//  private long cycles;
//  private long firstSourceTotal;
//  private long firstTargetTotal;
//  private long source_total;
//  private long target_total;
//  private long both;
//  private long source_only;
//  private long target_only;
//  private long row_passed;
//  private long row_failed;
//  private long speed;
//
//}
