package com.tapdata.tm.apiCalls.service;

import com.tapdata.tm.apiCalls.dto.ApiCallDto;
import com.tapdata.tm.apiCalls.vo.WorkerCallsInfo;
import com.tapdata.tm.apiServer.entity.WorkerCallEntity;
import com.tapdata.tm.apiServer.utils.PercentileCalculator;
import com.tapdata.tm.config.security.UserDetail;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for SupplementApiCallServer
 * 
 * <AUTHOR> href="<EMAIL>">Gavin'Xiao</a>
 * @version v1.0 2025/9/27 Create
 */
class SupplementApiCallServerTest {
    SupplementApiCallServer supplementApiCallServer;
    
    ApiCallService apiCallService;
    MongoTemplate mongoOperations;
    WorkerCallServiceImpl workerCallService;
    UserDetail loginUser;
    
    @BeforeEach
    void init() {
        loginUser = mock(UserDetail.class);
        when(loginUser.getUserId()).thenReturn("6393f084c162f518b18165c3");
        supplementApiCallServer = mock(SupplementApiCallServer.class);
        apiCallService = mock(ApiCallService.class);
        mongoOperations = mock(MongoTemplate.class);
        workerCallService = mock(WorkerCallServiceImpl.class);
        ReflectionTestUtils.setField(supplementApiCallServer, "apiCallService", apiCallService);
        ReflectionTestUtils.setField(supplementApiCallServer, "mongoOperations", mongoOperations);
        ReflectionTestUtils.setField(supplementApiCallServer, "workerCallService", workerCallService);
    }

    List<Long> delays(Long ... delays) {
        List<Long> list = new ArrayList<>();
        for (Long delay : delays) {
            list.add(delay);
        }
        return list;
    }
    
    @Nested
    class supplementTest {
        
        @Test
        void testNormal() {
            List<ApiCallDto> saveApiCallParamList = createTestApiCallDtos();

            when(apiCallService.save(saveApiCallParamList, loginUser)).thenReturn(saveApiCallParamList);
            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(Collections.emptyList());
            doCallRealMethod().when(supplementApiCallServer).supplement(saveApiCallParamList, loginUser);
            
            Assertions.assertDoesNotThrow(() -> supplementApiCallServer.supplement(saveApiCallParamList, loginUser));
        }
        
        @Test
        void testEmptyList() {
            List<ApiCallDto> emptyList = Collections.emptyList();
            
            doCallRealMethod().when(supplementApiCallServer).supplement(emptyList, loginUser);
            
            Assertions.assertDoesNotThrow(() -> supplementApiCallServer.supplement(emptyList, loginUser));
            verify(apiCallService, never()).save(any(), any());
        }
        
        @Test
        void testNullList() {
            
            doCallRealMethod().when(supplementApiCallServer).supplement(null, loginUser);
            
            Assertions.assertDoesNotThrow(() -> supplementApiCallServer.supplement(null, loginUser));
            verify(apiCallService, never()).save(any(), any());
        }
        
        @Test
        void testWithNullElements() {
            List<ApiCallDto> listWithNulls = new ArrayList<>();
            listWithNulls.add(createApiCallDto("worker1", "api1", "gateway1", 100L, "200", 1000L, 1100L));
            listWithNulls.add(null);
            listWithNulls.add(createApiCallDto("worker2", "api2", "gateway2", 200L, "404", 2000L, 2200L));
            listWithNulls.add(null);
            listWithNulls.add(createApiCallDto(null, "api2", "gateway2", 200L, "404", 2000L, 2200L));
            listWithNulls.add(createApiCallDto("worker2", null, "gateway2", 200L, "404", 2000L, 2200L));
            listWithNulls.add(createApiCallDto("", "api2", "gateway2", 200L, "404", 2000L, 2200L));
            listWithNulls.add(createApiCallDto("worker2", "", "gateway2", 200L, "404", 2000L, 2200L));


            when(apiCallService.save(listWithNulls, loginUser)).thenReturn(listWithNulls);
            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(Collections.emptyList());
            doCallRealMethod().when(supplementApiCallServer).supplement(listWithNulls, loginUser);
            
            Assertions.assertDoesNotThrow(() -> supplementApiCallServer.supplement(listWithNulls, loginUser));
        }
        
        @Test
        void testMultipleWorkersAndApis() {
            List<ApiCallDto> dtos = new ArrayList<>();
            // 同一个worker，不同API
            dtos.add(createApiCallDto("worker1", "api1", "gateway1", 100L, "200", 1000L, 1100L));
            dtos.add(createApiCallDto("worker1", "api2", "gateway1", 150L, "200", 2000L, 2150L));
            // 不同worker，同一个API
            dtos.add(createApiCallDto("worker2", "api1", "gateway2", 120L, "500", 3000L, 3120L));

            
            when(apiCallService.save(dtos, loginUser)).thenReturn(dtos);
            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(Collections.emptyList());
            doCallRealMethod().when(supplementApiCallServer).supplement(dtos, loginUser);
            
            Assertions.assertDoesNotThrow(() -> supplementApiCallServer.supplement(dtos, loginUser));
        }
    }
    
    @Nested
    class callUpdateTest {
        
        @Test
        void testNormal() {
            List<WorkerCallEntity> entities = createTestWorkerCallEntities();
            List<WorkerCallEntity> existingEntities = createExistingWorkerCallEntities();
            
            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(existingEntities);
            doCallRealMethod().when(supplementApiCallServer).callUpdate(entities);
            
            try (MockedStatic<PercentileCalculator> mockedCalculator = mockStatic(PercentileCalculator.class)) {
                mockedCalculator.when(() -> PercentileCalculator.calculatePercentile(any(), eq(0.5d))).thenReturn(100L);
                mockedCalculator.when(() -> PercentileCalculator.calculatePercentile(any(), eq(0.95d))).thenReturn(200L);
                mockedCalculator.when(() -> PercentileCalculator.calculatePercentile(any(), eq(0.99d))).thenReturn(250L);
                
                Assertions.assertDoesNotThrow(() -> supplementApiCallServer.callUpdate(entities));
            }
        }
        
        @Test
        void testEmptyList() {
            List<WorkerCallEntity> emptyList = Collections.emptyList();
            
            doCallRealMethod().when(supplementApiCallServer).callUpdate(emptyList);
            
            Assertions.assertDoesNotThrow(() -> supplementApiCallServer.callUpdate(emptyList));
            verify(mongoOperations, never()).find(any(Query.class), eq(WorkerCallEntity.class));
        }
        
        @Test
        void testNullList() {
            doCallRealMethod().when(supplementApiCallServer).callUpdate(null);
            
            Assertions.assertDoesNotThrow(() -> supplementApiCallServer.callUpdate(null));
            verify(mongoOperations, never()).find(any(Query.class), eq(WorkerCallEntity.class));
        }
        
        @Test
        void testWithExistingData() {
            List<WorkerCallEntity> entities = createTestWorkerCallEntities();
            List<WorkerCallEntity> existingEntities = createExistingWorkerCallEntities();
            
            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(existingEntities);
            doCallRealMethod().when(supplementApiCallServer).callUpdate(entities);
            
            try (MockedStatic<PercentileCalculator> mockedCalculator = mockStatic(PercentileCalculator.class)) {
                mockedCalculator.when(() -> PercentileCalculator.calculatePercentile(any(), anyDouble())).thenReturn(125L);
                
                Assertions.assertDoesNotThrow(() -> supplementApiCallServer.callUpdate(entities));
            }
        }
        
        @Test
        void testZeroRequests() {
            WorkerCallEntity entity = new WorkerCallEntity();
            entity.setWorkOid("worker1");
            entity.setApiId("api1");
            entity.setTimeStart(1000L);
            entity.setTimeGranularity(1);
            entity.setProcessId("process1");
            entity.setDelete(false);
            entity.setReqCount(0L);
            entity.setErrorCount(0L);
            entity.setDelays(new ArrayList<>());
            
            List<WorkerCallEntity> entities = Arrays.asList(entity);
            
            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(Collections.emptyList());
            doCallRealMethod().when(supplementApiCallServer).callUpdate(entities);
            
            try (MockedStatic<PercentileCalculator> mockedCalculator = mockStatic(PercentileCalculator.class)) {
                mockedCalculator.when(() -> PercentileCalculator.calculatePercentile(any(), anyDouble())).thenReturn(null);
                
                Assertions.assertDoesNotThrow(() -> supplementApiCallServer.callUpdate(entities));
            }
        }
        
        @Test
        void testNullDelays() {
            WorkerCallEntity entity = new WorkerCallEntity();
            entity.setWorkOid("worker1");
            entity.setApiId("api1");
            entity.setTimeStart(1000L);
            entity.setTimeGranularity(1);
            entity.setProcessId("process1");
            entity.setDelete(false);
            entity.setReqCount(5L);
            entity.setErrorCount(1L);
            entity.setDelays(null);
            
            List<WorkerCallEntity> entities = Arrays.asList(entity);
            
            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(Collections.emptyList());
            doCallRealMethod().when(supplementApiCallServer).callUpdate(entities);
            
            try (MockedStatic<PercentileCalculator> mockedCalculator = mockStatic(PercentileCalculator.class)) {
                mockedCalculator.when(() -> PercentileCalculator.calculatePercentile(any(), anyDouble())).thenReturn(null);
                
                Assertions.assertDoesNotThrow(() -> supplementApiCallServer.callUpdate(entities));
            }
        }
        
        @Test
        void testErrorRateCalculation() {
            WorkerCallEntity entity = new WorkerCallEntity();
            entity.setWorkOid("worker1");
            entity.setApiId("api1");
            entity.setTimeStart(1000L);
            entity.setTimeGranularity(1);
            entity.setProcessId("process1");
            entity.setDelete(false);
            entity.setReqCount(100L);
            entity.setErrorCount(10L);
            entity.setDelays(delays(100L, 200L));
            
            List<WorkerCallEntity> entities = Arrays.asList(entity);
            
            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(Collections.emptyList());
            doCallRealMethod().when(supplementApiCallServer).callUpdate(entities);
            
            try (MockedStatic<PercentileCalculator> mockedCalculator = mockStatic(PercentileCalculator.class)) {
                mockedCalculator.when(() -> PercentileCalculator.calculatePercentile(any(), anyDouble())).thenReturn(150L);
                
                Assertions.assertDoesNotThrow(() -> supplementApiCallServer.callUpdate(entities));
            }
        }
        
        @Test
        void testNullOptionalFields() {
            WorkerCallEntity entity = new WorkerCallEntity();
            entity.setWorkOid("worker1");
            entity.setApiId("api1");
            entity.setTimeStart(1000L);
            entity.setTimeGranularity(1);
            entity.setProcessId("process1");
            entity.setDelete(false);
            entity.setReqCount(null);
            entity.setErrorCount(null);
            entity.setDelays(delays(100L, 200L));
            
            List<WorkerCallEntity> entities = Arrays.asList(entity);
            
            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(Collections.emptyList());
            doCallRealMethod().when(supplementApiCallServer).callUpdate(entities);
            
            try (MockedStatic<PercentileCalculator> mockedCalculator = mockStatic(PercentileCalculator.class)) {
                mockedCalculator.when(() -> PercentileCalculator.calculatePercentile(any(), anyDouble())).thenReturn(150L);
                
                Assertions.assertDoesNotThrow(() -> supplementApiCallServer.callUpdate(entities));
            }
        }
        
        @Test
        void testExistingEntityWithNullDelays() {
            WorkerCallEntity newEntity = new WorkerCallEntity();
            newEntity.setWorkOid("worker1");
            newEntity.setApiId("api1");
            newEntity.setTimeStart(1000L);
            newEntity.setTimeGranularity(1);
            newEntity.setProcessId("process1");
            newEntity.setDelete(false);
            newEntity.setReqCount(10L);
            newEntity.setErrorCount(2L);
            newEntity.setDelays(delays(100L, 150L));
            
            WorkerCallEntity existingEntity = new WorkerCallEntity();
            existingEntity.setWorkOid("worker1");
            existingEntity.setApiId("api1");
            existingEntity.setReqCount(5L);
            existingEntity.setErrorCount(1L);
            existingEntity.setDelays(null);
            
            List<WorkerCallEntity> entities = Arrays.asList(newEntity);
            List<WorkerCallEntity> existingEntities = Arrays.asList(existingEntity);
            
            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(existingEntities);
            doCallRealMethod().when(supplementApiCallServer).callUpdate(entities);
            
            try (MockedStatic<PercentileCalculator> mockedCalculator = mockStatic(PercentileCalculator.class)) {
                mockedCalculator.when(() -> PercentileCalculator.calculatePercentile(any(), anyDouble())).thenReturn(125L);
                
                Assertions.assertDoesNotThrow(() -> supplementApiCallServer.callUpdate(entities));
            }
        }
        
        @Test
        void testMultipleMatchingEntities() {
            WorkerCallEntity newEntity = new WorkerCallEntity();
            newEntity.setWorkOid("worker1");
            newEntity.setApiId("api1");
            newEntity.setTimeStart(1000L);
            newEntity.setTimeGranularity(1);
            newEntity.setProcessId("process1");
            newEntity.setDelete(false);
            newEntity.setReqCount(10L);
            newEntity.setErrorCount(2L);
            newEntity.setDelays(delays(100L, 150L));
            
            WorkerCallEntity existingEntity1 = new WorkerCallEntity();
            existingEntity1.setWorkOid("worker1");
            existingEntity1.setApiId("api1");
            existingEntity1.setReqCount(5L);
            existingEntity1.setErrorCount(1L);
            existingEntity1.setDelays(delays(120L));
            
            WorkerCallEntity existingEntity2 = new WorkerCallEntity();
            existingEntity2.setWorkOid("worker1");
            existingEntity2.setApiId("api1");
            existingEntity2.setReqCount(3L);
            existingEntity2.setErrorCount(0L);
            existingEntity2.setDelays(delays(80L, 90L));
            
            List<WorkerCallEntity> entities = Arrays.asList(newEntity);
            List<WorkerCallEntity> existingEntities = Arrays.asList(existingEntity1, existingEntity2);
            
            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(existingEntities);
            doCallRealMethod().when(supplementApiCallServer).callUpdate(entities);
            
            try (MockedStatic<PercentileCalculator> mockedCalculator = mockStatic(PercentileCalculator.class)) {
                mockedCalculator.when(() -> PercentileCalculator.calculatePercentile(any(), anyDouble())).thenReturn(100L);
                
                Assertions.assertDoesNotThrow(() -> supplementApiCallServer.callUpdate(entities));
            }
        }
    }
    
    // Helper methods
    private List<ApiCallDto> createTestApiCallDtos() {
        List<ApiCallDto> dtos = new ArrayList<>();
        dtos.add(createApiCallDto("worker1", "api1", "gateway1", 100L, "200", 1000L, 1100L));
        dtos.add(createApiCallDto("worker1", "api2", "gateway1", 200L, "404", 2000L, 2200L));
        dtos.add(createApiCallDto("worker2", "api1", "gateway2", 150L, "500", 3000L, 3150L));
        return dtos;
    }
    
    private ApiCallDto createApiCallDto(String workOid, String allPathId, String apiGatewayUuid, 
                                       Long latency, String code, Long reqTime, Long resTime) {
        ApiCallDto dto = new ApiCallDto();
        dto.setWorkOid(workOid);
        dto.setAllPathId(allPathId);
        dto.setApi_gateway_uuid(apiGatewayUuid);
        dto.setLatency(latency);
        dto.setCode(code);
        dto.setReqTime(reqTime);
        dto.setResTime(resTime);
        return dto;
    }
    
    private List<WorkerCallEntity> createTestWorkerCallEntities() {
        List<WorkerCallEntity> entities = new ArrayList<>();
        
        WorkerCallEntity entity1 = new WorkerCallEntity();
        entity1.setWorkOid("worker1");
        entity1.setApiId("api1");
        entity1.setTimeStart(1000L);
        entity1.setTimeGranularity(1);
        entity1.setProcessId("process1");
        entity1.setDelete(false);
        entity1.setReqCount(10L);
        entity1.setErrorCount(2L);
        entity1.setDelays(delays(100L, 150L, 200L));
        entities.add(entity1);
        
        WorkerCallEntity entity2 = new WorkerCallEntity();
        entity2.setWorkOid("worker2");
        entity2.setApiId("api2");
        entity2.setTimeStart(2000L);
        entity2.setTimeGranularity(1);
        entity2.setProcessId("process2");
        entity2.setDelete(false);
        entity2.setReqCount(5L);
        entity2.setErrorCount(0L);
        entity2.setDelays(delays(80L, 90L, 110L));
        entities.add(entity2);
        
        return entities;
    }
    
    private List<WorkerCallEntity> createExistingWorkerCallEntities() {
        List<WorkerCallEntity> entities = new ArrayList<>();
        
        WorkerCallEntity existing1 = new WorkerCallEntity();
        existing1.setWorkOid("worker1");
        existing1.setApiId("api1");
        existing1.setReqCount(5L);
        existing1.setErrorCount(1L);
        existing1.setDelays(Arrays.asList(120L, 130L));
        entities.add(existing1);
        
        return entities;
    }

    @Nested
    class sortAndProcessListTest {

        @Test
        void testSortByReqTime() {
            List<ApiCallDto> dtos = new ArrayList<>();
            // 创建时间乱序的数据
            dtos.add(createApiCallDto("worker1", "api1", "gateway1", 100L, "200", 3000L, 3100L)); // 最晚
            dtos.add(createApiCallDto("worker1", "api1", "gateway1", 150L, "200", 1000L, 1150L)); // 最早
            dtos.add(createApiCallDto("worker1", "api1", "gateway1", 120L, "200", 2000L, 2120L)); // 中间

            when(apiCallService.save(dtos, loginUser)).thenReturn(dtos);
            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(Collections.emptyList());
            doCallRealMethod().when(supplementApiCallServer).supplement(dtos, loginUser);

            // 测试排序逻辑通过supplement方法间接验证
            Assertions.assertDoesNotThrow(() -> supplementApiCallServer.supplement(dtos, loginUser));
        }
    }

    @Nested
    class convertToWorkerCallsInfoTest {

        @Test
        void testFieldMapping() {
            ApiCallDto dto = new ApiCallDto();
            dto.setWorkOid("test-worker");
            dto.setAllPathId("test-api");
            dto.setApi_gateway_uuid("test-gateway");
            dto.setLatency(250L);
            dto.setCode("201");
            dto.setReqTime(5000L);
            dto.setResTime(5250L);

            List<ApiCallDto> dtos = Arrays.asList(dto);
            when(apiCallService.save(dtos, loginUser)).thenReturn(dtos);
            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(Collections.emptyList());
            doCallRealMethod().when(supplementApiCallServer).supplement(dtos, loginUser);

            // 通过验证后续调用来间接验证转换逻辑正确执行
            Assertions.assertDoesNotThrow(() -> supplementApiCallServer.supplement(dtos, loginUser));
        }
    }

    @Nested
    class edgeCasesTest {

        @Test
        void testLine114ErrorCountCalculation() {
            // 测试第114行的错误计算逻辑
            WorkerCallEntity newEntity = new WorkerCallEntity();
            newEntity.setWorkOid("worker1");
            newEntity.setApiId("api1");
            newEntity.setTimeStart(1000L);
            newEntity.setTimeGranularity(1);
            newEntity.setProcessId("process1");
            newEntity.setDelete(false);
            newEntity.setReqCount(10L);
            newEntity.setErrorCount(null); // null errorCount to test line 114
            newEntity.setDelays(delays(100L, 150L));

            WorkerCallEntity existingEntity = new WorkerCallEntity();
            existingEntity.setWorkOid("worker1");
            existingEntity.setApiId("api1");
            existingEntity.setReqCount(5L);
            existingEntity.setErrorCount(3L);
            existingEntity.setDelays(delays(120L, 130L));

            List<WorkerCallEntity> entities = Arrays.asList(newEntity);
            List<WorkerCallEntity> existingEntities = Arrays.asList(existingEntity);

            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(existingEntities);
            doCallRealMethod().when(supplementApiCallServer).callUpdate(entities);

            try (MockedStatic<PercentileCalculator> mockedCalculator = mockStatic(PercentileCalculator.class)) {
                mockedCalculator.when(() -> PercentileCalculator.calculatePercentile(any(), anyDouble())).thenReturn(125L);

                Assertions.assertDoesNotThrow(() -> supplementApiCallServer.callUpdate(entities));
            }
        }

        @Test
        void testComplexQueryBuilding() {
            List<WorkerCallEntity> entities = new ArrayList<>();

            WorkerCallEntity entity1 = new WorkerCallEntity();
            entity1.setWorkOid("worker1");
            entity1.setApiId("api1");
            entity1.setTimeStart(1000L);
            entity1.setTimeGranularity(1);
            entity1.setProcessId("process1");
            entity1.setDelete(false);
            entity1.setReqCount(10L);
            entity1.setErrorCount(2L);
            entity1.setDelays(delays(100L, 150L));
            entities.add(entity1);

            WorkerCallEntity entity2 = new WorkerCallEntity();
            entity2.setWorkOid("worker2");
            entity2.setApiId("api2");
            entity2.setTimeStart(2000L);
            entity2.setTimeGranularity(1);
            entity2.setProcessId("process2");
            entity2.setDelete(false);
            entity2.setReqCount(5L);
            entity2.setErrorCount(0L);
            entity2.setDelays(delays(80L, 90L));
            entities.add(entity2);

            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(Collections.emptyList());
            doCallRealMethod().when(supplementApiCallServer).callUpdate(entities);

            try (MockedStatic<PercentileCalculator> mockedCalculator = mockStatic(PercentileCalculator.class)) {
                mockedCalculator.when(() -> PercentileCalculator.calculatePercentile(any(), anyDouble())).thenReturn(100L);

                Assertions.assertDoesNotThrow(() -> supplementApiCallServer.callUpdate(entities));
            }
        }

        @Test
        void testEmptyDelaysList() {
            WorkerCallEntity entity = new WorkerCallEntity();
            entity.setWorkOid("worker1");
            entity.setApiId("api1");
            entity.setTimeStart(1000L);
            entity.setTimeGranularity(1);
            entity.setProcessId("process1");
            entity.setDelete(false);
            entity.setReqCount(5L);
            entity.setErrorCount(1L);
            entity.setDelays(new ArrayList<>()); // empty delays list

            List<WorkerCallEntity> entities = Arrays.asList(entity);

            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(Collections.emptyList());
            doCallRealMethod().when(supplementApiCallServer).callUpdate(entities);

            try (MockedStatic<PercentileCalculator> mockedCalculator = mockStatic(PercentileCalculator.class)) {
                mockedCalculator.when(() -> PercentileCalculator.calculatePercentile(any(), anyDouble())).thenReturn(null);

                Assertions.assertDoesNotThrow(() -> supplementApiCallServer.callUpdate(entities));
            }
        }

        @Test
        void testExistingEntityWithEmptyDelays() {
            WorkerCallEntity newEntity = new WorkerCallEntity();
            newEntity.setWorkOid("worker1");
            newEntity.setApiId("api1");
            newEntity.setTimeStart(1000L);
            newEntity.setTimeGranularity(1);
            newEntity.setProcessId("process1");
            newEntity.setDelete(false);
            newEntity.setReqCount(10L);
            newEntity.setErrorCount(2L);
            newEntity.setDelays(delays(100L, 150L));

            WorkerCallEntity existingEntity = new WorkerCallEntity();
            existingEntity.setWorkOid("worker1");
            existingEntity.setApiId("api1");
            existingEntity.setReqCount(5L);
            existingEntity.setErrorCount(1L);
            existingEntity.setDelays(new ArrayList<>()); // empty delays

            List<WorkerCallEntity> entities = Arrays.asList(newEntity);
            List<WorkerCallEntity> existingEntities = Arrays.asList(existingEntity);

            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(existingEntities);
            doCallRealMethod().when(supplementApiCallServer).callUpdate(entities);

            try (MockedStatic<PercentileCalculator> mockedCalculator = mockStatic(PercentileCalculator.class)) {
                mockedCalculator.when(() -> PercentileCalculator.calculatePercentile(any(), anyDouble())).thenReturn(125L);

                Assertions.assertDoesNotThrow(() -> supplementApiCallServer.callUpdate(entities));
            }
        }

        @Test
        void testNoMatchingExistingEntities() {
            List<WorkerCallEntity> entities = createTestWorkerCallEntities();

            // 返回不匹配的已存在实体
            WorkerCallEntity nonMatchingEntity = new WorkerCallEntity();
            nonMatchingEntity.setWorkOid("different-worker");
            nonMatchingEntity.setApiId("different-api");
            nonMatchingEntity.setReqCount(5L);
            nonMatchingEntity.setErrorCount(1L);
            nonMatchingEntity.setDelays(delays(120L, 130L));

            List<WorkerCallEntity> existingEntities = Arrays.asList(nonMatchingEntity);

            when(mongoOperations.find(any(Query.class), eq(WorkerCallEntity.class))).thenReturn(existingEntities);
            doCallRealMethod().when(supplementApiCallServer).callUpdate(entities);

            try (MockedStatic<PercentileCalculator> mockedCalculator = mockStatic(PercentileCalculator.class)) {
                mockedCalculator.when(() -> PercentileCalculator.calculatePercentile(any(), anyDouble())).thenReturn(100L);

                Assertions.assertDoesNotThrow(() -> supplementApiCallServer.callUpdate(entities));
            }
        }
    }
}
