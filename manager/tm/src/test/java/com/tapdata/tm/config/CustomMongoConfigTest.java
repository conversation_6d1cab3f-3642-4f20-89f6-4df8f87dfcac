package com.tapdata.tm.config;

import com.mongodb.MongoException;
import com.mongodb.MongoNamespace;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.InsertManyOptions;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.CollectionOptions;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CustomMongoConfigTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private MongoDatabase mongoDatabase;

    @Mock
    private MongoCollection<org.bson.Document> mongoCollection;

    @Mock
    private MongoCollection<org.bson.Document> backupCollection;

    @Mock
    private MongoCursor<org.bson.Document> mongoCursor;

    @Mock
    private CappedCollection cappedCollectionAnnotation;

    private CustomMongoConfig customMongoConfig;

    @BeforeEach
    void setUp() {
        customMongoConfig = new CustomMongoConfig(mongoTemplate);
    }

    @Test
    void testConstructor() {
        // Given & When
        CustomMongoConfig config = new CustomMongoConfig(mongoTemplate);

        // Then
        assertNotNull(config);
    }

    @Test
    void testInit() {
        // Given - init方法会扫描类路径中的@CappedCollection注解

        // When & Then - 验证方法执行不抛异常
        assertDoesNotThrow(() -> customMongoConfig.init());
    }

    @Test
    void testHandleCappedCollection_CollectionNotExists() {
        // Given
        String collectionName = "newCollection";
        when(mongoTemplate.collectionExists(collectionName)).thenReturn(false);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        customMongoConfig.handleCappedCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoTemplate).createCollection(eq(collectionName), any(CollectionOptions.class));
    }

    @Test
    void testHandleCappedCollection_CollectionExists() {
        // Given
        String collectionName = "existingCollection";
        when(mongoTemplate.collectionExists(collectionName)).thenReturn(true);
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getName()).thenReturn("testDb");
        when(mongoDatabase.getCollection(collectionName)).thenReturn(mongoCollection);
        when(mongoCollection.countDocuments()).thenReturn(0L);

        Document collStats = new Document();
        collStats.put("capped", false);
        collStats.put("maxSize", "0");
        collStats.put("max", "0");
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        customMongoConfig.handleCappedCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoCollection).renameCollection(any(MongoNamespace.class)); // recreate collection
    }

    @Test
    void testHandleCappedCollection_Exception() {
        // Given
        String collectionName = "errorCollection";
        when(mongoTemplate.collectionExists(collectionName)).thenThrow(new RuntimeException("Database error"));

        // When & Then
        assertDoesNotThrow(() -> 
            customMongoConfig.handleCappedCollection(collectionName, cappedCollectionAnnotation)
        );
    }

    @Test
    void testCreateCappedCollection_WithValidValues() {
        // Given
        String collectionName = "newCappedCollection";
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        customMongoConfig.createCappedCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoTemplate).createCollection(eq(collectionName), any(CollectionOptions.class));
    }

    @Test
    void testCreateCappedCollection_WithZeroValues() {
        // Given
        String collectionName = "newCappedCollection";
        when(cappedCollectionAnnotation.maxLength()).thenReturn(0L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(0L);

        // When
        customMongoConfig.createCappedCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoTemplate).createCollection(eq(collectionName), any(CollectionOptions.class));
    }

    @Test
    void testCreateCappedCollection_WithNegativeValues() {
        // Given
        String collectionName = "newCappedCollection";
        when(cappedCollectionAnnotation.maxLength()).thenReturn(-1L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(-1L);

        // When
        customMongoConfig.createCappedCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoTemplate).createCollection(eq(collectionName), any(CollectionOptions.class));
    }

    @Test
    void testUpdateExistingCollection_NotCapped() {
        // Given
        String collectionName = "regularCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getName()).thenReturn("testDb");
        when(mongoDatabase.getCollection(collectionName)).thenReturn(mongoCollection);
        when(mongoCollection.countDocuments()).thenReturn(0L);

        Document collStats = new Document();
        collStats.put("capped", false);
        collStats.put("maxSize", "0");
        collStats.put("max", "0");
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        customMongoConfig.updateExistingCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoCollection).renameCollection(any(MongoNamespace.class)); // recreate
    }

    @Test
    void testUpdateExistingCollection_CappedSameAttributes() {
        // Given
        String collectionName = "cappedCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);

        Document collStats = new Document();
        collStats.put("capped", true);
        collStats.put("maxSize", "1024");
        collStats.put("max", "1000");
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        customMongoConfig.updateExistingCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoDatabase, times(1)).runCommand(any(Document.class)); // Only stats check
        verify(mongoCollection, never()).renameCollection(any(MongoNamespace.class)); // No recreation
    }

    @Test
    void testUpdateExistingCollection_CappedDifferentAttributes() {
        // Given
        String collectionName = "cappedCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getName()).thenReturn("testDb");
        when(mongoDatabase.getCollection(collectionName)).thenReturn(mongoCollection);
        when(mongoCollection.countDocuments()).thenReturn(0L);

        Document collStats = new Document();
        collStats.put("capped", true);
        collStats.put("maxSize", "1024");
        collStats.put("max", "1000");
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        when(cappedCollectionAnnotation.maxLength()).thenReturn(2000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(2048L);

        // When
        customMongoConfig.updateExistingCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoCollection).renameCollection(any(MongoNamespace.class));
        verify(mongoTemplate).createCollection(anyString(), any(CollectionOptions.class));
    }

    @Test
    void testUpdateExistingCollection_Exception() {
        // Given
        String collectionName = "errorCollection";
        when(mongoTemplate.getDb()).thenThrow(new RuntimeException("Database error"));

        // When & Then
        assertDoesNotThrow(() -> 
            customMongoConfig.updateExistingCollection(collectionName, cappedCollectionAnnotation)
        );
    }

    @Test
    void testGetCollectionStats_Success() {
        // Given
        String collectionName = "testCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        
        Document collStats = new Document();
        collStats.put("capped", true);
        collStats.put("maxSize", "1024");
        collStats.put("max", "100");
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        // When
        CustomMongoConfig.CollectionStats result = customMongoConfig.getCollectionStats(collectionName);

        // Then
        assertNotNull(result);
        assertTrue(result.isCapped());
        assertEquals(1024L, result.getMaxSize());
        assertEquals(100L, result.getMax());
    }

    @Test
    void testGetCollectionStats_Exception() {
        // Given
        String collectionName = "errorCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.runCommand(any(Document.class))).thenThrow(new RuntimeException("Database error"));

        // When
        CustomMongoConfig.CollectionStats result = customMongoConfig.getCollectionStats(collectionName);

        // Then
        assertNotNull(result);
        assertFalse(result.isCapped());
        assertEquals(0L, result.getMaxSize());
        assertEquals(0L, result.getMax());
    }

    @Test
    void testGetCollectionStats_NullValues() {
        // Given
        String collectionName = "testCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        
        Document collStats = new Document();
        collStats.put("capped", true);
        // maxSize and max are null
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        // When
        CustomMongoConfig.CollectionStats result = customMongoConfig.getCollectionStats(collectionName);

        // Then
        assertNotNull(result);
        assertTrue(result.isCapped());
        assertEquals(-1L, result.getMaxSize()); // null maxSize becomes -1L
        assertEquals(0L, result.getMax()); // null max becomes 0L
    }

    @Test
    void testNeedsUpdate_WithMaxMemory_DifferentMax() {
        // Given - 注意新代码中needsUpdate的逻辑变了：stats.getMax() != annotation.maxLength()
        CustomMongoConfig.CollectionStats stats = new CustomMongoConfig.CollectionStats(true, 1024L, 1000L);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(2000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        boolean result = customMongoConfig.needsUpdate(stats, cappedCollectionAnnotation);

        // Then
        assertTrue(result);
    }

    @Test
    void testNeedsUpdate_WithMaxMemory_DifferentMaxSize() {
        // Given
        CustomMongoConfig.CollectionStats stats = new CustomMongoConfig.CollectionStats(true, 1024L, 1000L);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(2048L);

        // When
        boolean result = customMongoConfig.needsUpdate(stats, cappedCollectionAnnotation);

        // Then
        assertTrue(result);
    }

    @Test
    void testNeedsUpdate_WithMaxMemory_Same() {
        // Given
        CustomMongoConfig.CollectionStats stats = new CustomMongoConfig.CollectionStats(true, 1024L, 1000L);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        boolean result = customMongoConfig.needsUpdate(stats, cappedCollectionAnnotation);

        // Then
        assertFalse(result);
    }

    @Test
    void testNeedsUpdate_WithoutMaxMemory_DifferentMax() {
        // Given
        CustomMongoConfig.CollectionStats stats = new CustomMongoConfig.CollectionStats(true, 1024L, 1000L);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(2000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(0L);

        // When
        boolean result = customMongoConfig.needsUpdate(stats, cappedCollectionAnnotation);

        // Then
        assertTrue(result);
    }

    @Test
    void testNeedsUpdate_WithoutMaxMemory_Same() {
        // Given
        CustomMongoConfig.CollectionStats stats = new CustomMongoConfig.CollectionStats(true, 1024L, 1000L);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(0L);

        // When
        boolean result = customMongoConfig.needsUpdate(stats, cappedCollectionAnnotation);

        // Then
        assertFalse(result);
    }

    @Test
    void testRecreateCappedCollection_Success() {
        // Given
        String collectionName = "cappedCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getName()).thenReturn("testDb");
        when(mongoDatabase.getCollection(collectionName)).thenReturn(mongoCollection);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        customMongoConfig.recreateCappedCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoCollection).renameCollection(any(MongoNamespace.class));
        verify(mongoTemplate).createCollection(anyString(), any(CollectionOptions.class));
    }

    @Test
    void testRecreateCappedCollection_Exception() {
        // Given
        String collectionName = "errorCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getCollection(collectionName)).thenThrow(new RuntimeException("Rename error"));

        // When & Then
        assertDoesNotThrow(() ->
            customMongoConfig.recreateCappedCollection(collectionName, cappedCollectionAnnotation)
        );
    }

    @Test
    void testCollectionStats_Constructor() {
        // Given
        boolean capped = true;
        long maxSize = 1024L;
        long max = 100L;

        // When
        CustomMongoConfig.CollectionStats stats = new CustomMongoConfig.CollectionStats(capped, maxSize, max);

        // Then
        assertNotNull(stats);
        assertEquals(capped, stats.isCapped());
        assertEquals(maxSize, stats.getMaxSize());
        assertEquals(max, stats.getMax());
    }

    @Test
    void testCollectionStats_GettersWithFalseValues() {
        // Given
        boolean capped = false;
        long maxSize = 0L;
        long max = 0L;

        // When
        CustomMongoConfig.CollectionStats stats = new CustomMongoConfig.CollectionStats(capped, maxSize, max);

        // Then
        assertNotNull(stats);
        assertFalse(stats.isCapped());
        assertEquals(0L, stats.getMaxSize());
        assertEquals(0L, stats.getMax());
    }

    @Test
    void testRecreateCappedCollection_WithDataMigration() {
        // Given
        String collectionName = "cappedCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getName()).thenReturn("testDb");
        when(mongoDatabase.getCollection(collectionName)).thenReturn(mongoCollection);
        when(mongoDatabase.getCollection(anyString())).thenReturn(backupCollection);
        when(backupCollection.countDocuments()).thenReturn(5L);
        when(backupCollection.find()).thenReturn(mock(com.mongodb.client.FindIterable.class));
        when(backupCollection.find().iterator()).thenReturn(mongoCursor);

        // Mock cursor with some documents
        org.bson.Document doc1 = new org.bson.Document("_id", 1);
        org.bson.Document doc2 = new org.bson.Document("_id", 2);
        when(mongoCursor.hasNext()).thenReturn(true, true, false);
        when(mongoCursor.next()).thenReturn(doc1, doc2);

        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        customMongoConfig.recreateCappedCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoCollection).renameCollection(any(MongoNamespace.class));
        verify(mongoTemplate).createCollection(anyString(), any(CollectionOptions.class));
        verify(mongoCollection).insertMany(any(List.class), any(InsertManyOptions.class));
    }

    @Test
    void testRecreateCappedCollection_WithBatchInsertException() {
        // Given
        String collectionName = "cappedCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getName()).thenReturn("testDb");
        when(mongoDatabase.getCollection(collectionName)).thenReturn(mongoCollection);
        when(mongoDatabase.getCollection(anyString())).thenReturn(backupCollection);
        when(backupCollection.countDocuments()).thenReturn(5L);
        when(backupCollection.find()).thenReturn(mock(com.mongodb.client.FindIterable.class));
        when(backupCollection.find().iterator()).thenReturn(mongoCursor);

        // Mock cursor with documents
        org.bson.Document doc = new org.bson.Document("_id", 1);
        when(mongoCursor.hasNext()).thenReturn(true, false);
        when(mongoCursor.next()).thenReturn(doc);

        // Mock insertMany to throw MongoException
        when(mongoCollection.insertMany(any(List.class), any(InsertManyOptions.class)))
            .thenThrow(new MongoException("Capped collection full"));

        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When & Then
        assertDoesNotThrow(() ->
            customMongoConfig.recreateCappedCollection(collectionName, cappedCollectionAnnotation)
        );
    }

    @Test
    void testRecreateCappedCollection_DropBackupCollection() {
        // Given
        String collectionName = "cappedCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getName()).thenReturn("testDb");
        when(mongoDatabase.getCollection(collectionName)).thenReturn(mongoCollection);
        when(mongoDatabase.getCollection(anyString())).thenReturn(backupCollection);
        when(backupCollection.countDocuments()).thenReturn(5L).thenReturn(0L);
        when(backupCollection.find()).thenReturn(mock(com.mongodb.client.FindIterable.class));
        when(backupCollection.find().iterator()).thenReturn(mongoCursor);
        when(mongoCursor.hasNext()).thenReturn(false);
        when(mongoCollection.countDocuments()).thenReturn(5L);

        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        customMongoConfig.recreateCappedCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(backupCollection).drop();
    }

    @Test
    void testRecreateCappedCollection_LargeBatch() {
        // Given
        String collectionName = "cappedCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getName()).thenReturn("testDb");
        when(mongoDatabase.getCollection(collectionName)).thenReturn(mongoCollection);
        when(mongoDatabase.getCollection(anyString())).thenReturn(backupCollection);
        when(backupCollection.countDocuments()).thenReturn(5000L);
        when(backupCollection.find()).thenReturn(mock(com.mongodb.client.FindIterable.class));
        when(backupCollection.find().iterator()).thenReturn(mongoCursor);

        // Mock cursor with many documents to trigger batch processing
        org.bson.Document doc = new org.bson.Document("_id", 1);
        when(mongoCursor.hasNext()).thenReturn(true);
        when(mongoCursor.next()).thenReturn(doc);

        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        customMongoConfig.recreateCappedCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoCollection).renameCollection(any(MongoNamespace.class));
        verify(mongoTemplate).createCollection(anyString(), any(CollectionOptions.class));
    }
}
