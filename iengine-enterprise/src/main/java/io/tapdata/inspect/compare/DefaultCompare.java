package io.tapdata.inspect.compare;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.hazelcast.aggregation.impl.CanonicalizingHashSet;
import com.tapdata.constant.CommonUtil;
import com.tapdata.constant.JSONUtil;
import com.tapdata.constant.StringUtil;
import com.tapdata.entity.MysqlJson;
import com.tapdata.exception.CompareException;
import io.tapdata.entity.schema.value.DateTime;
import io.tapdata.inspect.util.InspectJobUtil;
import org.apache.commons.lang3.StringUtils;
import io.tapdata.pdk.core.utils.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.types.ObjectId;

import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2020/8/18 1:10 下午
 * @description
 */
public class DefaultCompare implements CompareFunction<Map<String, Object>, String> {

	private final Logger logger = LogManager.getLogger(DefaultCompare.class);

	private List<String> sourceColumns;
	private List<String> targetColumns;

	public boolean isIgnoreTimePrecision() {
		return ignoreTimePrecision;
	}

	public void setIgnoreTimePrecision(boolean ignoreTimePrecision) {
		this.ignoreTimePrecision = ignoreTimePrecision;
	}

	private boolean ignoreTimePrecision = false;
	private String roundingMode;

	public String getRoundingMode() {
		return roundingMode;
	}

	public void setRoundingMode(String roundingMode) {
		this.roundingMode = roundingMode;
	}

	public DefaultCompare() {
	}

	public DefaultCompare(List<String> sourceColumns, List<String> targetColumns) {
		this.sourceColumns = sourceColumns;
		this.targetColumns = targetColumns;
		if (null != sourceColumns && null != targetColumns) {
			if (sourceColumns.size() != targetColumns.size()) {
				throw new RuntimeException("The number of fields from the source and target is inconsistent: " + sourceColumns.size() + ", " + targetColumns.size());
			}
		}
	}

	@Override
	public String apply(Map<String, Object> t1, Map<String, Object> t2, String sourceId, String targetId) throws CompareException {
		if (t1 == null && t2 == null) {
			return null;
		}
		if (t1 == null) {
			return "Source record is null";
		} else if (t2 == null) {
			return "Target record is null";
		}

		boolean isSetColumns = null != sourceColumns && null != targetColumns;
		Set<String> differentFields;
		if (isSetColumns) {
			differentFields = new LinkedHashSet<>();
			for (int i = 0, len = sourceColumns.size(); i < len; i++) {
				Object val1 = t1.get(sourceColumns.get(i));
				Object val2 = t2.get(targetColumns.get(i));
                try {
                    if (compare(val1, val2)) {
                        differentFields.add(String.valueOf(i));
                    }
                } catch (CompareException e) {
                    throw e.sourceTable(sourceId).targetTable(targetId).sourceField(sourceColumns.get(i)).targetField(targetColumns.get(i));
                }
            }
		} else {
			// compare base on t1's columns
			Set<String> sets = new LinkedHashSet<>();
			sets.addAll(t1.keySet());
			sets.addAll(t2.keySet());
			List<String> columns = sets.stream().sorted().collect(Collectors.toList());
            differentFields = new HashSet<>();
            for (String key : columns) {
                Object val1 = t1.get(key);
                Object val2 = t2.get(key);
                try {
                    if (compare(val1, val2)) {
                        differentFields.add(key);
                    }
                } catch (CompareException e) {
                    throw e.sourceTable(sourceId).targetTable(targetId).sourceField(key).targetField(key);
                }
            }
		}

		if (differentFields.size() != 0) {
			if (logger.isDebugEnabled()) {
				int maxLength = differentFields.stream().map(String::length).max(Comparator.comparingInt(r -> r)).orElse(20);
				String msg = differentFields.stream().map(field -> {
					Object val1 = t1.get(field);
					Object val2 = t2.get(field);
					String msg1 = val1 == null ? "null" : (val1 + " (" + val1.getClass().getTypeName() + ")");
					String msg2 = val2 == null ? "null" : (val2 + " (" + val2.getClass().getTypeName() + ")");
					return appendSpace(field, maxLength) + ": " + appendSpace(msg1, 50) + " ->     " + msg2;
				}).collect(Collectors.joining("\n ", "\n ", "\n"));
				logger.debug(sourceId + " -> " + targetId + ": " + msg);
//      } else {
//        logger.info(sourceId + " -> " + targetId + ": " + String.join(", ", differentFields));
			}
		}

		if (differentFields.isEmpty()) {
			return null;
		} else {
			if (isSetColumns) {
				return "Different index:" + String.join(",", differentFields);
			} else {
				return "Different fields:" + String.join(",", differentFields);
			}
		}
	}

	public String appendSpace(String str, int length) {
		StringBuilder sb = new StringBuilder(str == null ? "" : str);
		while (sb.length() < length) {
			sb.append(' ');
		}
		sb.append(' ');
		return sb.toString();
	}

	// true: error   false: ok
	protected boolean compare(Object val1, Object val2) throws CompareException {
		return CommonUtil.compare(val1, val2, ignoreTimePrecision, roundingMode);
	}

}
