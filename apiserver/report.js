const log = require('./dist').log.app;
const axios = require('axios');
const path = require('path');
// const appConfig = require('./config');
const Conf = require('conf');
const config = new Conf();

const { getToken, removeToken } = require('./tapdata');

const hostname = require('os').hostname();
// const startTime = new Date().getTime();
const apiServerStatus = {
	worker_status: {}
};
let lastReportData = '';

const report = function (data, token) {
	// const configPath = path.join(__dirname, 'config.json');

	const reportServerUrl = config.get('tapDataServer.url') + '/api/Workers/upsertWithWhere?access_token=' + token;

	if (!reportServerUrl || !reportServerUrl)
		return;

	data = Object.assign(data || {}, config.get('reportData'));

	// data['start_time'] = startTime;
	//	data['ping_time'] = new Date().getTime();
	//data['worker_ip'] = hostname;
	data['hostname'] = hostname;
	data['port'] = config.get('port');
	if (apiServerStatus.worker_status.total_thread) {
		data['total_thread'] = apiServerStatus.worker_status.total_thread;
		delete apiServerStatus.worker_status.total_thread;
	}

	if (apiServerStatus.worker_status.running_thread) {
		data['running_thread'] = apiServerStatus.worker_status.running_thread;
		delete apiServerStatus.worker_status.running_thread;
	}
	data['version'] = config.get('version');

	Object.assign(data, apiServerStatus);

	try {
		// delete data.worker_status.workers;
		let reportData = JSON.stringify(data);
		if( lastReportData !== reportData){
			log.info('report data', JSON.stringify(data));
			lastReportData = reportData;
		}
		const params = {process_id:config.get('reportData.process_id'),worker_type:config.get('reportData.worker_type')};
		const url = reportServerUrl + '&where=' + encodeURI(JSON.stringify(params));

		axios.post(url, data, {
			headers: {
				'Content-Type': 'application/json'
			},
			timeout: 5000
		}).then(response => {
			log.debug(`report complete:`, response.data);
		}).catch(error => {
			if (error.response) {
				if (error.response.status === 401 || error.response.status === 403) {
					console.error('Access token Expired');
					removeToken();
				} else {
					log.error('report fail', error.response.data);
				}
			} else {
				log.error('report fail', error.message);
			}
		});
	} catch (e) {
		log.error('report fail', e);
	}
};

let model = config.get('model') || 'cloud';
log.info('Current run model is ' + model);
if (model === 'cloud') {
	log.info('start report api server status to management');
	setInterval(() => {
		getToken(token => {
			if (token)
				report(null, token)
		})
	}, config.get('reportIntervals') || 5000);
}

exports.setStatus = function (status) {
	Object.assign(apiServerStatus, status);
	getToken(token => {
		if (token)
			report(null, token)
	})
};
