/**
 * 验证 404 错误过滤逻辑
 * 简单测试过滤条件是否正确
 */

console.log('🔍 验证 404 错误过滤逻辑\n');

// 测试不同类型的错误
const testErrors = [
    {
        name: 'NotFoundError 测试',
        error: { name: 'NotFoundError', statusCode: 404, message: 'Endpoint not found' },
        shouldLog: false
    },
    {
        name: '纯 404 状态码测试',
        error: { name: 'HttpError', statusCode: 404, message: 'Not found' },
        shouldLog: false
    },
    {
        name: '纯 NotFoundError 名称测试',
        error: { name: 'NotFoundError', statusCode: 500, message: 'Not found but 500' },
        shouldLog: false
    },
    {
        name: '500 内部错误测试',
        error: { name: 'InternalError', statusCode: 500, message: 'Internal server error' },
        shouldLog: true
    },
    {
        name: '400 错误请求测试',
        error: { name: 'BadRequestError', statusCode: 400, message: 'Bad request' },
        shouldLog: true
    },
    {
        name: '未定义状态码测试',
        error: { name: 'UnknownError', message: 'Unknown error' },
        shouldLog: true
    }
];

console.log('📊 错误过滤测试结果');
console.log('=' .repeat(70));
console.log('| 错误类型                | 名称           | 状态码 | 应该记录 | 实际结果 |');
console.log('|------------------------|----------------|--------|----------|----------|');

testErrors.forEach(test => {
    const err = test.error;
    
    // 这是我们在 sequence.ts 中实现的过滤逻辑
    const shouldLog = err.name !== 'NotFoundError' && err.statusCode !== 404;
    
    const result = shouldLog === test.shouldLog ? '✅ 正确' : '❌ 错误';
    const statusCode = err.statusCode || '未定义';
    
    console.log(`| ${test.name.padEnd(22)} | ${(err.name || '未定义').padEnd(14)} | ${statusCode.toString().padEnd(6)} | ${test.shouldLog ? '是      ' : '否      '} | ${result.padEnd(8)} |`);
});

console.log('\n🧪 详细逻辑验证');
console.log('-' .repeat(50));

testErrors.forEach((test, index) => {
    const err = test.error;
    const shouldLog = err.name !== 'NotFoundError' && err.statusCode !== 404;
    
    console.log(`\n${index + 1}. ${test.name}:`);
    console.log(`   错误名称: ${err.name || '未定义'}`);
    console.log(`   状态码: ${err.statusCode || '未定义'}`);
    console.log(`   过滤条件: name !== 'NotFoundError' (${err.name !== 'NotFoundError'}) && statusCode !== 404 (${err.statusCode !== 404})`);
    console.log(`   结果: ${shouldLog ? '记录日志' : '不记录日志'}`);
    console.log(`   期望: ${test.shouldLog ? '记录日志' : '不记录日志'}`);
    console.log(`   状态: ${shouldLog === test.shouldLog ? '✅ 正确' : '❌ 错误'}`);
});

console.log('\n📝 实现的过滤规则:');
console.log('```typescript');
console.log('if (err.name !== "NotFoundError" && err.statusCode !== 404) {');
console.log('    log.app.error(`${reqId} process request error`, err);');
console.log('}');
console.log('```');

console.log('\n🎯 过滤效果:');
console.log('✅ 不会记录的错误:');
console.log('   - name === "NotFoundError" 的错误');
console.log('   - statusCode === 404 的错误');
console.log('   - 同时满足上述两个条件的错误');

console.log('\n✅ 仍会记录的错误:');
console.log('   - 500 内部服务器错误');
console.log('   - 400 错误请求');
console.log('   - 401 未授权错误');
console.log('   - 403 禁止访问错误');
console.log('   - 其他所有非 404 的错误');

console.log('\n🔧 使用场景:');
console.log('当客户端请求不存在的端点时:');
console.log('- 错误: NotFoundError: Endpoint "GET /api/v1/xxx/x9thfsd41v2" not found.');
console.log('- 状态: 不会在错误日志中记录');
console.log('- 响应: 仍然正常返回 404 给客户端');

console.log('\n🎉 验证完成! 404 错误过滤功能正常工作。');
