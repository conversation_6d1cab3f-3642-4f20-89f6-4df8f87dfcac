import { inject } from '@loopback/context';
import {
	<PERSON><PERSON><PERSON><PERSON>, HttpErrors,
	InvokeMethod,
	<PERSON>rseParams,
	Reject,
	RequestContext,
	RestBindings,
	Send,
	SequenceHandler,
} from '@loopback/rest';
import { AuthenticationBindings, AuthenticateFn } from '@loopback/authentication';
import { log } from './log';
import { RateLimiter } from './rate-limiter';
import {getConfig, updateConfig} from './utils/configUtil';
const Conf = require('conf');
const config = new Conf();

/**
 * When an exception occurs,
 * if you do not need certain exception information to appear in the return result or want to block unnecessary attributes,
 * you can add the path of the attribute here to ignore it,
 * and support the use of '.' segmentation
 * */
const ignorePropInError = ['reason','$clusterTime']

const SequenceActions = RestBindings.SequenceActions;
const excludeAuthPath = ['/', '/explorer', '/openapi.json'];
const processId = config.get("reportData.process_id");
async function queryApiId(path:string, method:string) {
	const axios = require("axios");
	let port = config.get('port') || process.env.PORT || 3030;
	let response = await axios.get('http://127.0.0.1:'+port+'/openapi.json');
	let openapi:any;
	let apiId: string = '';
	try{
		openapi = response.data;
		apiId = openapi['paths'][path][method.toLowerCase()]['x-api-id'];
		return apiId;
	}catch(e){
		console.error(e);
	}
	return apiId;
}

export class MySequence implements SequenceHandler {

	constructor(
		@inject(SequenceActions.FIND_ROUTE) protected findRoute: FindRoute,
		@inject(SequenceActions.PARSE_PARAMS) protected parseParams: ParseParams,
		@inject(SequenceActions.INVOKE_METHOD) protected invoke: InvokeMethod,
		@inject(SequenceActions.SEND) public send: Send,
		@inject(SequenceActions.REJECT) public reject: Reject,
		@inject(AuthenticationBindings.AUTH_ACTION) protected authenticateRequest: AuthenticateFn,
	) {
	}

	async handle(context: RequestContext) {
		let apiAuditLog = {};
		const { request, response } = context;
		const pid = process.pid;
		const workOid = getConfig(`runningPidMappingWorkId.${pid}`, null)
		let resEndHandler = async (err: Error) => {
			const _end = new Date().getTime();
			//@ts-ignore
			apiAuditLog.workOid = workOid;
			//@ts-ignore
			apiAuditLog.latency = _end - _start;
			//@ts-ignore
			apiAuditLog.reqTime = _start;
			//@ts-ignore
			apiAuditLog.resTime = _end;
			if (err) {
				log.app.error(err);
			}

			//@ts-ignore
			let user_info: object = request['user_info'] || {};
			//@ts-ignore
			let api_meta: object = request['api_meta'] || { options: {} };
			//@ts-ignore
			apiAuditLog.api_meta = api_meta;
			//@ts-ignore
			apiAuditLog['user_info'] = user_info;
			//@ts-ignore
			apiAuditLog.user_id = user_info.user_id;
			//@ts-ignore
			apiAuditLog.allPathId = api_meta.options.allPathId;
			//@ts-ignore
			apiAuditLog.api_path = api_meta.options.pathTpl;
			//@ts-ignore
			apiAuditLog.api_name = api_meta.options.apiName;
			//@ts-ignore
			apiAuditLog.call_id = reqId;
			//@ts-ignore
			apiAuditLog.user_ip = `${ip}`;
			//@ts-ignore
			apiAuditLog.user_ips = request.ips;
			//@ts-ignore
			apiAuditLog.user_port = `${port}`;
			//@ts-ignore
			apiAuditLog.req_path = request.path;
			//@ts-ignore
			apiAuditLog.method = request.method;
			//@ts-ignore
			apiAuditLog.api_gateway_ip = request.connection.localAddress;
			//@ts-ignore
			apiAuditLog.api_gateway_port = request.connection.localPort;
			//@ts-ignore
			apiAuditLog.api_worker_ip = request.connection.localAddress;
			//@ts-ignore
			apiAuditLog.api_worker_port = request.connection.localPort;

			// https://stackoverflow.com/questions/38423930/how-to-retrieve-client-and-server-ip-address-and-port-number-in-node-js

			//@ts-ignore
			apiAuditLog.api_worker_uuid = processId;
			//@ts-ignore
			apiAuditLog.api_gateway_uuid = processId;
			//@ts-ignore
			apiAuditLog.req_headers = request.headers;
			//@ts-ignore
			apiAuditLog.req_bytes = request.socket.bytesRead;
			//@ts-ignore
			apiAuditLog.code = response.statusCode;
			//@ts-ignore
			apiAuditLog.codeMsg = response.statusMessage + (apiAuditLog.codeMsg || '');

			// log.app.debug(response.connection);

			// log.app.debug(request.socket.server);
			//@ts-ignore
			apiAuditLog.req_bytes = request.socket.bytesRead;
			//@ts-ignore
			apiAuditLog.res_bytes = request.socket.bytesWritten;
			if( request.query.log){
				//@ts-ignore
				apiAuditLog.log = request.query.log;
			}
			//@ts-ignore
			apiAuditLog.function = response.__fun;
			// console.log('apiAuditLog@resEndHandler@src/sequence.ts:141\n', apiAuditLog);
			//@ts-ignore
			apiAuditLog.errorCode = response.tapErrorCode;
			//@ts-ignore
			apiAuditLog.errorMsg = response.tapErrorMsg;
			//@ts-ignore
			if(response.tapErrorCode && response.tapErrorCode !== ''){
				//@ts-ignore
				apiAuditLog.code = 500;
				//@ts-ignore
				apiAuditLog.codeMsg = (apiAuditLog.codeMsg || '') + response.tapErrorMsg
			}
			//cachedApiStats.set(apiAuditLog.call_id, apiAuditLog);
			// 如果请求本身是 404，则不汇报
			//@ts-ignore
			if (apiAuditLog.code !== 404 && typeof process.send === 'function') {
				//补充request.api_meta丢失的情况
				//@ts-ignore
				if (apiAuditLog.allPathId === undefined || null == apiAuditLog.allPathId || '' === apiAuditLog.allPathId) {
					//@ts-ignore
					const apiId = await queryApiId(apiAuditLog.req_path, apiAuditLog.method);
					//@ts-ignore
					apiAuditLog.allPathId = apiId
				}

				//@ts-ignore
				//log.app.info("data: " + JSON.stringify(apiAuditLog))
				process.send({
					type: 'apiCell',
					data: apiAuditLog
				});
			}
		};
		response.on('finish', resEndHandler);
		response.on('error', resEndHandler);

		const ip = request.headers['x-forwarded-for'] || request.connection.remoteAddress;
		const port = request.headers['x-forwarded-port'] || request.connection.remotePort;
		const _start = new Date().getTime();
		const reqId = `reqId_${_start}_${(Math.random() + '').slice(2)}`;
		let ignoreLog = false;
		try {
			if (request.query) {
				//@ts-ignore
				apiAuditLog.query = JSON.parse(JSON.stringify(request.query));
				//@ts-ignore
				delete apiAuditLog.query.access_token;
				//@ts-ignore
				apiAuditLog.query = JSON.stringify(apiAuditLog.query);
			}
			if (request.body) {
				//@ts-ignore
				apiAuditLog.body = JSON.stringify(apiAuditLog.body);
			}
			//@ts-ignore
			apiAuditLog.requestHeaders = JSON.stringify(request.headers | {});

			const route = this.findRoute(request);
			// 认证
			if (excludeAuthPath.includes(request.path)) {
				log.app.debug('exclude auth path ' + request.path);
			} else {
				//认证，认证后才能获取到request.api_meta
				let rt = await this.authenticateRequest(request);
			}

			const isShuttingDown = getConfig(`workerRunInfo.${process.pid}.isShuttingDown`, false);
			if (isShuttingDown) {
				const rejectMsg = 'The worker restart is entering the restart phase, and new requests will no longer be processed. Please wait for all connections to complete and for the worker to restart before accessing again';
				log.app.info(`Worker id [${process.pid}] | ${rejectMsg}`);
				let activeCount = getConfig(`workerRunInfo.${process.pid}.activeCount`, 0);
				if (activeCount && !isNaN(activeCount) && Number(activeCount) > 0) {
					log.app.info(`The remaining ${activeCount} request is currently being executed, and the worker will be restarted after the execution is completed`);
				}
				const error = new HttpErrors.ServiceUnavailable(rejectMsg);
				error.statusCode = 430;
				error.status = 430;
				error.workId = process.pid;
				delete error.expose;
				throw error;
			}

			const args = await this.parseParams(request, route);
			const contentType: string = request.header('content-type') || '';
			const params = contentType.indexOf('multipart/form-data') >= 0 ? "multipart/form-data" : JSON.stringify(args);
			log.app.debug(`${reqId} client ${ip}, ${request.method} ${request.path}, param ${params}, query ${JSON.stringify(request.query)}`);
			//@ts-ignore
			apiAuditLog.req_params = `${params}`;

			// 速率限制检查
			const rateLimiter = RateLimiter.getInstance();
			// @ts-ignore
			const apiMeta = request.api_meta;
			if (apiMeta && apiMeta.options && apiMeta.options.originalConfig) {
				const originalConfig = apiMeta.options.originalConfig;
				const apiId = originalConfig.apiId ||originalConfig._id || originalConfig.id || request.path;
				// 解析限速值，如果没有配置或解析失败，默认为1 QPS
				const parsedLimit = parseInt(originalConfig.limit);
				const configuredLimit = isNaN(parsedLimit) ? 1 : parsedLimit;

				// 如果限速值 <= 0，跳过限速逻辑
				if (configuredLimit <= 0) {
					log.app.debug(`Rate limiting disabled for API ${apiId}, limit: ${configuredLimit}`);
				} else {
					// Get detailed limit calculation info
					const limitInfo = rateLimiter.getLimitCalculationInfo(configuredLimit)

					const isAllowed = rateLimiter.checkRateLimit(apiId, { limit: configuredLimit });
					if (!isAllowed) {
						const error = rateLimiter.createRateLimitError(configuredLimit);
						log.app.warn(`Rate limit exceeded for API ${apiId}, ${limitInfo.description}, worker ${limitInfo.workerCount}, process: ${process.pid}`);
						ignoreLog = true;
						throw error;
					}

					// Log successful rate limit check (only for debugging, can be removed in production)
					log.app.debug(`Rate limit check passed for API ${apiId}, ${limitInfo.description}, worker ${limitInfo.workerCount}, process: ${process.pid}`);
				}
			}
			//await sleep(1000)
			let result = await this.invoke(route, args);

			//log.app.debug('result@src/sequence.ts:180\n', result);
			if (result) {
				if (result.data) {
					//@ts-ignore
					apiAuditLog.res_rows = result.data.length ? result.data.length : 0;

					if (config.get("filterNull")) {
						// @ts-ignore
						result.data.forEach(row => {
							for (const key in row) {
								if (row.hasOwnProperty(key)) {
									const element = row[key];
									if ( element === "null" || element === "" || element === null || element === undefined) {
										delete row[key];
									}
								}
							}
						});
					}

				} else {
					//@ts-ignore
					apiAuditLog.res_rows = 1;
				}
			}

			const filename = request.query.filename;
			const type = request.query.type || 'json';
			if (filename) {
				response.setHeader('Content-Disposition', 'attachment; filename="' + filename + '"');
				result = this.convertToBuffer(type, result);
			} else if (result && result.filename && result.stream) { // download file from gridfs
				response.setHeader('Content-Disposition', 'attachment; filename="' + result.filename + '"');
				result = result.stream;
			}
			if( result instanceof Promise) {
				result
					.then( res => this.send(response, res))
					.catch(err => this.reject(context, err));
			} else {
				this.send(response, result);
			}

		} catch (err) {
			//@ts-ignore
			apiAuditLog.codeMsg = this.formatErrorMsg(apiAuditLog.codeMsg, err);
			//@ts-ignore
			if (err.stack) {
				//@ts-ignore
				log.app.error(`${reqId} call api failed, stack: ${err.stack}`);
				//@ts-ignore
				delete err.stack
			}
			this.removeIgnore(err)
			// @ts-ignore
			this.reject(context, err);

			// 不记录 404 NotFoundError 日志
			// @ts-ignore
			if (ignoreLog || (err.name !== 'NotFoundError' && err.statusCode !== 404)) {
				log.app.error(`${reqId} process request error`, err);
			}
		} finally {
			let activeCount = getConfig(`workerRunInfo.${process.pid}.activeCount`, 0);
			if (activeCount && !isNaN(activeCount) && Number(activeCount) > 0) {
				updateConfig(`workerRunInfo.${process.pid}.activeCount`, activeCount--);
			}
		}
	}

	// tslint:disable-next-line:no-any
	async convertToBuffer(type: string | any, data: object) {
		log.app.info(`export data to ${type}`);
		type = type ? type.toLowerCase() : 'json';
		if (type === 'json') {
			return Buffer.from(JSON.stringify(data), 'utf8');
		} else if (type === 'csv') {
			let separatedBy = ',';
			let delimiter = '"';
			data = data || {};
			// @ts-ignore
			let records = Array.isArray(data['data']) ? data['data'] : [data['data']];
			if (records.length > 0) {
				let fields = Object.keys(records[0]);
				let contents: string[] = [];
				let row: string[] = [];
				fields.forEach(v => row.push(`${delimiter}${v}${delimiter}`));
				contents.push(row.join(separatedBy));
				records.forEach((record: object) => {
					// tslint:disable-next-line:no-shadowed-variable
					let row: string[] = [];
					// @ts-ignore
					fields.forEach((field :string) => {
						// @ts-ignore
						let value = record[field] || '';

						if( value instanceof Date) {
							value = value.toISOString();
						} else if( typeof value === 'object'){
							value = JSON.stringify(value)
						}

						row.push(`${delimiter}${value}${delimiter}`)
					});
					contents.push(row.join(separatedBy));
				});
				let content = contents.join("\n");
				return Buffer.concat([
					Buffer.from([-17, -69, -65]), // add unicode bom
					Buffer.from(content, 'utf8')
				])
			} else {
				return Buffer.alloc(0);
			}
		} else if ( type === 'excel') {

			// @ts-ignore
			let records = Array.isArray(data['data']) ? data['data'] : [data['data']];

			if( records && records.length > 0) {

				const Excel = require('exceljs');
				const wb = new Excel.Workbook();

				const ws = wb.addWorksheet('Sheet 1');

				let fields = Object.keys(records[0]);

				// header
				ws.addRow(fields);

				records.forEach( (record: object) => {
					// tslint:disable-next-line:no-any
					let rowData: any[] = [];
					fields.forEach((field :string) => {
						// @ts-ignore
						let value = record[field] || '';

						if( value instanceof Date) {
							value = value.toISOString();
						} else if( typeof value === 'object'){
							value = JSON.stringify(value)
						}
						rowData.push(value);
					});
					ws.addRow(rowData);
				});

				return await wb.xlsx.writeBuffer(); // return
			} else {
				return Buffer.alloc(0);
			}
		} else {
			return Buffer.alloc(0);
		}
	}

	//@ts-ignore
	formatErrorMsg(suffix: string,err) {
		// @ts-ignore
		const existingMsg = suffix || "";
		let codePart = " ";
		if (err.code) {
			codePart = ` - (${err.code})`;
		} else if (err.statusCode) {
			codePart = ` - [${err.statusCode}]`;
		}
		const messagePart =
			err.message ||
			err.details ||
			err.details.syntaxError ||
			"";
		const fullMsg = `${existingMsg}${codePart} ${messagePart}`;
		// const stackPart = err.stack
		// 	? err.stack.length > 1024
		// 		? err.stack.slice(0, 1024)
		// 		: err.stack
		// 	: "No stack trace";
		// return `${fullMsg}\n${stackPart}`;
		return `${fullMsg}`;
	}

	//@ts-ignore
	removeIgnore(err) {
		ignorePropInError.forEach(prop => {
			let item = err
			let paths = prop.split('.');
			for (let index = 0; index < paths.length - 1; index++) {
				item = item[paths[index]];
				if (!item) {
					break;
				}
			}
			if (item && item[paths[paths.length - 1]]) {
				delete item[paths[paths.length - 1]];
			}
		});
	}
}