import {Count, CountSchema, Filter, repository, Where} from '@loopback/repository';
import {renameFields} from '../utils/fieldChangeName';
import {filterFields, js_traverse, findAllField} from '../utils/filterFields';
import {loggerInterceptor} from '../utils/loggerIntercept';
import {deepReplaceInPlace, builderWhere} from '../utils/whereScanner';
import {checkField} from '../utils/fieldCovert';
import {
  post,
  param,
  get,
  getFilterSchemaFor,
  getWhereSchemaFor,
  patch,
  del,
  requestBody,
  HttpErrors,
  RestBindings,
  RequestContext, Request,Response
} from '@loopback/rest';
import {<%= modelName %>} from '../models';
import {<%= repositoryName %>} from '../repositories';
import {
  AuthenticationBindings,
  UserProfile,
  authenticate,
} from '@loopback/authentication';
import {inject,intercept,InvocationContext,ValueOrPromise,InvocationResult,Interceptor} from '@loopback/context';
import {importExcel} from '../importExcel';
import {log} from '../log';
const Conf = require('conf');
const config = new Conf();
const defaultPageLimit = 20;
const defaultPageIndex = 1;

@intercept(loggerInterceptor)
export class <%= className %>Controller {

  constructor(
    @repository(<%= repositoryName %>)
    public <%= repositoryNameCamel %> : <%= repositoryName %>,
  ) {}

  <%_ Object.entries(api).forEach(([path, val]) => { -%>
    <%_ if (val['type'] === 'preset') { -%>
        <%_ if (val['name'] === 'create') { -%>
        <%- include('./partials/create.ejs', {val, modelName, repositoryNameCamel, modelVariableName, name, tableName, apiId, apiName, originalConfig}) %>
        <%_ } else if (val['name'] === 'findPage') { -%>
        <%- include('./partials/findPage.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig}) %>
        <%_ } else if (val['name'] === 'findById') { -%>
        <%- include('./partials/findById.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig, idType}) %>
        <%_ } else if (val['name'] === 'updateById') { -%>
        <%- include('./partials/updateById.ejs', {val, modelName, repositoryNameCamel, modelVariableName, name, tableName, apiId, apiName, originalConfig, idType}) %>
        <%_ } else if (val['name'] === 'deleteById') { -%>
        <%- include('./partials/deleteById.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig, idType}) %>
        <%_ } else if (val['name'] === 'downloadById') { -%>
        <%- include('./partials/downloadById.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig, idType}) %>
        <%_ } else if (val['name'] === 'download') { -%>
        <%- include('./partials/download.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig}) %>
        <%_ } else if (val['name'] === 'upload') { -%>
        <%- include('./partials/upload.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig}) %>
        <%_ }  %>
    <%_ } else if (val['name'] === 'customerQuery') { -%>
    <%- include('./partials/customerQuery.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig}) %>
    <%_ } else if (val['method'] === 'STREAM') { -%>
    <%- include('./partials/stream.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig, dataSource}) %>
    <%_ } else { -%>
    <%- include('./partials/custom.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig}) %>
    <%_ } -%>
  <%_ }) -%>
}
