package com.tapdata.tm.alarm.constant;

public class AlarmConstant {
    private AlarmConstant(){

    }

    public static final String TASK_NAME = "taskName";
    public static final String ERROR_TIME = "errorTime";
    public static final String ERROR_LOG = "errorLog";
    public static final String ERROR_EVENT = "errorEvent";
    public static final String COMPLETION_TIME="completionTime";
    public static final String DELAY_TIME = "delayTime";
    public static final String STOP_TIME = "stopTime";
    public static final String NODE_NAME="nodeName";
    public static final String OCCURRED_TIME="occurredTime";
    public static final String CURRENT_VALUE="currentValue";
    public static final String INSPECT_NAME = "inspectName";
    public static final String ALARM_DATE = "alarmDate";
    public static final String COST_TIME="costTime";
    public static final String SNAP_DONE_DATE = "snapDoneDate";
    public static final String TASK_ID = "taskId";
    public static final String INSPECT_ID = "inspectId";

}
